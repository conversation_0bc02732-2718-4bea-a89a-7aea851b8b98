﻿namespace LeadTeams.Desktop.View.Reports
{
    public partial class EmployeeWithAttendanceLogReport : GenericViewTable.GenericEmployeeWithAttendanceLogReportView
    {
        private readonly ISession _session;
        private readonly IAttendanceService _attendanceService;

        public EmployeeWithAttendanceLogReport(ISession session, IAttendanceService attendanceService)
        {
            InitializeComponent();

            _session = session;
            _attendanceService = attendanceService;
            DataBaseWatcherEntityName = nameof(AttendanceLogModel);

            AssociateAndRaiseEvents();

            RefreshListFunction();
        }

        private void AssociateAndRaiseEvents()
        {
            tpdPeriodValue.ValueChanged += (s, e) => RefreshListFunction();
            dgvMain.GetDgvTotalsSummary.DGV.DataBindingComplete += (s, e) =>
            {
                var lst = MainList.Items.ToList();

                if (!dgvMain.GetDgvTotalsSummary.DGV.Columns.Contains(RunningTotal))
                    dgvMain.GetDgvTotalsSummary.DGV.Columns.Add(RunningTotal);

                if (e.ListChangedType != ListChangedType.Reset) return;

                long total = 0;
                for (int i = 0; i < lst.Count; ++i)
                {
                    AttendanceLogReport a = lst[i];
                    DataGridViewRow r = dgvMain.GetDgvTotalsSummary.DGV.Rows[i];
                    total += a.TotalSeconds;
                    var timeSpan = TimeSpan.FromSeconds(total);
                    string value = $"{timeSpan.Hours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
                    r.Cells[nameof(RunningTotal)].Value = value;
                }
            };
        }


        public override PaginationList<AttendanceLogReport> MainData()
        {
            if (tpdPeriodValue.Value == Ulid.Empty)
                return new PaginationList<AttendanceLogReport>(new List<AttendanceLogReport>(), 0, PageNumber, PageSize);
            string groupBy = TargetTimeDurationEnumeration.FromValue(tpdPeriodValue.Value).Name;
            List<AttendanceLogReport> result = _attendanceService.GetEmployeeAttendanceLog(groupBy, _session.Organization.Id);
            return PaginationList<AttendanceLogReport>.Create(result, PageNumber, PageSize);
        }

        //Methods
        protected override void AddViewListControl()
        {
            tableLayoutPanel1.SetColumnSpan(dgvMain, 2);
            tableLayoutPanel1.Controls.Add(dgvMain, 0, 1);

            //Select Column Name To Sum
            dgvMain.GetDgvTotalsSummary.SummaryColumns = new string[] { nameof(AttendanceLogReport.TotalSeconds) };
        }
    }
}
