﻿namespace LeadTeams.API.Controllers.Business
{
    public class ScreensAccessProfileController : BaseBusinessController<ScreensAccessProfileModel, ScreensAccessProfileModel, CreateScreensAccessProfileViewModel, UpdateScreensAccessProfileViewModel>
    {
        private readonly IScreensAccessProfileService _screensAccessProfileService;

        public ScreensAccessProfileController(IScreensAccessProfileService screensAccessProfileService) : base(screensAccessProfileService)
        {
            _screensAccessProfileService = screensAccessProfileService;
        }

        [HttpGet("GetScreensAccessProfileDetailsForScreensAccessProfile")]
        public IActionResult GetScreensAccessProfileDetailsForScreensAccessProfile(Ulid screensAccessProfileId)
        {
            try
            {
                var result = _screensAccessProfileService.GetScreensAccessProfileDetailsForScreensAccessProfile(screensAccessProfileId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
