﻿namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class BaseReportView : LeadTeamsForm
    {
        protected IDataSourceHandler DataSourceHandler;
        protected string DataBaseWatcherEntityName;

        public BaseReportView()
        {
            InitializeComponent();

            AssociateAndRaiseEvents();

            btnClearFilter.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            btnClearFilter.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            btnClearFilter.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color8;
            btnClearFilter.RadiusProperties.BorderColor = Color.Transparent;
            btnClearFilter.RadiusProperties.BorderRadius = 10;
            btnClearFilter.RadiusProperties.BorderSize = 0;
            lblSearchValues.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font3;
            tlpFilter.Controls.Add(btnClearFilter, 0, 0);
            tlpFilter.Controls.Add(lblSearchValues, 1, 0);

            DataBaseWatcher.OnDataBaseWatcherChanged += OnDataBaseWatcherChanged;
        }

        private void AssociateAndRaiseEvents()
        {
            // Use AsyncButtonHandler to prevent multiple clicks on buttons that trigger potentially long operations
            tsbtnGenerate.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => RefreshListFunction()));
            });

            tsbtnExport.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => ExportFunction()));
            });

            tsbtnFilter.HandleAsyncClickSafe(async () =>
            {
                await Task.Run(() => this.SafelyInvokeAction(() => FilterFunction()));
            });

            tsbtnClose.Click += (s, e) => this.Close();
        }

        private void OnDataBaseWatcherChanged(DataBaseEntity dataBaseEntity)
        {
            if (string.IsNullOrEmpty(DataBaseWatcherEntityName))
                return;
            if (DataBaseWatcherEntityName == dataBaseEntity.EntityType)
                HandleDataHasChanged();
        }

        private void HandleDataHasChanged()
        {
            this.SafelyInvokeAction(async () =>
            {
                Color baseColor = tsbtnGenerate.BackColor;
                // Flashing effect to indicate update
                for (int i = 0; i < 20; i++)
                {
                    tsbtnGenerate.BackColor = i % 2 == 0 ? Color.Yellow : Color.Orange;
                    await Task.Delay(500); // Pause for 500 milliseconds
                }
                tsbtnGenerate.BackColor = baseColor; // Reset to default
            });
        }

        #region Buttons Actions Events
        public virtual void RefreshListFunction() { }
        public virtual void FilterFunction()
        {
            IsSearched = true;
        }
        public virtual void ExportFunction() { }
        #endregion

        #region Filter Action Handlers
        #region Fields - Properties
        protected List<ControlsValueModel> listControlValue { get; set; } = new List<ControlsValueModel>();
        private LeadTeamsLabel lblSearchValues = new LeadTeamsLabel()
        {
            Name = "lblSearchValues",
            Anchor = AnchorStyles.Right | AnchorStyles.Left,
            TextAlign = ContentAlignment.MiddleLeft,
        };
        private LeadTeamsButton btnClearFilter = new LeadTeamsButton()
        {
            Name = "btnClearFilter",
            Anchor = AnchorStyles.Right | AnchorStyles.Left,
            Text = "Clear Filter",
        };
        private LeadTeamsTableLayoutPanel tlpFilter = new LeadTeamsTableLayoutPanel()
        {
            Dock = DockStyle.Fill,
            RowCount = 1,
            ColumnCount = 2,
        };
        public DataGridViewColumn RunningTotal = new DataGridViewColumn()
        {
            Name = "RunningTotal",
            HeaderText = "Balance",
            ValueType = typeof(decimal),
            ReadOnly = true,
            CellTemplate = new DataGridViewTextBoxCell(),
        };
        protected bool IsSearched
        {
            set
            {
                if (value)
                {
                    lblSearchValues.Text = SearchUtilities.GetListControlsValues(listControlValue);
                    if (tlpMain.RowCount == 1)
                    {
                        tlpMain.RowCount = 2;
                        tlpMain.RowStyles.Add(new RowStyle() { Height = 35, SizeType = SizeType.Absolute });
                        tlpMain.Controls.Add(tlpFilter, 0, 1);
                        tlpMain.Size = new Size(tlpMain.Size.Width, 55 + 35);
                    }
                }
                else
                {
                    if (tlpMain.RowCount == 2)
                    {
                        Clear();
                    }
                }
            }
        }
        protected DateTime TransactionDateFrom => ValidateValue.ValidateDateTime(SearchUtilities.GetSearchValue(listControlValue)).Date;
        protected DateTime TransactionDateTo => ValidateValue.ValidateDateTime(SearchUtilities.GetSearchValue(listControlValue)).Date;
        #endregion

        #region Methods
        public virtual void Clear()
        {
            Utilities.RemoveArbitraryRow(tlpMain, 1);
            listControlValue = new List<ControlsValueModel>();
            tlpMain.Size = new Size(tlpMain.Size.Width, 55);
            RefreshListFunction();
        }
        #endregion
        #endregion
    }
}
