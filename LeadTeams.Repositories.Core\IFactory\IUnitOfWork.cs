﻿namespace LeadTeams.Repositories.Core.IFactory
{
    public interface IUnitOfWork : IBaseUnitOfWork
    {
        IBaseRepository<AllowanceModel, AllowanceModel> Allowance { get; }
        IBaseRepository<AskLeaveModel, AskLeaveViewModel> AskLeave { get; }
        IAttendanceLogRepository<AttendanceLogModel, AttendanceLogViewModel> AttendanceLog { get; }
        IAuditRepository<AuditModel> Audit { get; }
        IEmployeeAllowanceRepository<EmployeeAllowanceModel, EmployeeAllowanceModel> EmployeeAllowance { get; }
        IEmployeeEducationalQualificationRepository<EmployeeEducationalQualificationModel, EmployeeEducationalQualificationModel> EmployeeEducationalQualification { get; }
        IEmployeeKidsRepository<EmployeeKidsModel, EmployeeKidsModel> EmployeeKids { get; }
        IEmployeeManagerRepository<EmployeeManagerModel, EmployeeManagerModel> EmployeeManager { get; }
        IEmployeeRepository<EmployeeModel, EmployeeViewModel> Employee { get; }
        IManagementTeamEmployeeRepository<ManagementTeamEmployeeModel, ManagementTeamEmployeeModel> ManagementTeamEmployee { get; }
        IBaseRepository<ManagementTeamModel, ManagementTeamModel> ManagementTeam { get; }
        IManagementTeamManagerRepository<ManagementTeamManagerModel, ManagementTeamManagerModel> ManagementTeamManager { get; }
        IMeetingEmployeeRepository<MeetingEmployeeModel, MeetingEmployeeModel> MeetingEmployees { get; }
        IBaseRepository<MeetingModel, MeetingViewModel> Meeting { get; }
        IMessageRepository<MessageModel, MessageModel> Message { get; }
        IBaseRepository<OrganizationModel, OrganizationModel> Organization { get; }
        IBaseRepository<OrganizationNewsModel, OrganizationNewsModel> OrganizationNews { get; }
        IBaseRepository<ProjectModel, ProjectModel> Project { get; }
        IBaseRepository<ScreensAccessProfileModel, ScreensAccessProfileModel> ScreensAccessProfile { get; }
        IBaseRepository<ScreensAccessProfileDetailsModel, ScreensAccessProfileDetailsModel> ScreensAccessProfileDetails { get; }
        IRefreshTokenRepository<RefreshTokenModel, RefreshTokenModel> RefreshToken { get; }
        IScreenShotsMonitoringRepository<ScreenShotsMonitoringModel, ScreenShotsMonitoringViewModel> ScreenShotsMonitoring { get; }
        IBaseRepository<ShiftModel, ShiftModel> Shift { get; }
        IShiftDynamicPatternRepository<ShiftDynamicPatternModel, ShiftDynamicPatternModel> ShiftDynamicPattern { get; }
        IShiftFixedPatternRepository<ShiftFixedPatternModel, ShiftFixedPatternModel> ShiftFixedPattern { get; }
        IBaseRepository<SettingModel, SettingModel> Setting { get; }
        ITaskRepository<TaskModel, TaskViewModel> Task { get; }
        IUserRepository<UserModel, UserViewModel> User { get; }
    }
}
