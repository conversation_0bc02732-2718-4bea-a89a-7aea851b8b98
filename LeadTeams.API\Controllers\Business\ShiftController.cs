﻿namespace LeadTeams.API.Controllers.Business
{
    public class ShiftController : BaseBusinessController<ShiftModel, ShiftModel, CreateShiftViewModel, UpdateShiftViewModel>
    {
        private readonly IShiftService _shiftService;

        public ShiftController(IShiftService shiftService) : base(shiftService)
        {
            _shiftService = shiftService;
        }

        [HttpGet("GetShiftDynamicPatternsForShift")]
        public IActionResult GetShiftDynamicPatternsForShift(Ulid shiftId)
        {
            try
            {
                var result = _shiftService.GetShiftDynamicPatternsForShift(shiftId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetShiftFixedPatternsForShift")]
        public IActionResult GetShiftFixedPatternsForShift(Ulid shiftId)
        {
            try
            {
                var result = _shiftService.GetShiftFixedPatternsForShift(shiftId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetShiftDynamicPatterns")]
        public IActionResult GetShiftDynamicPatterns()
        {
            try
            {
                var result = _shiftService.GetShiftDynamicPatterns();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetShiftFixedPatterns")]
        public IActionResult GetShiftFixedPatterns()
        {
            try
            {
                var result = _shiftService.GetShiftFixedPatterns();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetTargetHours")]
        public IActionResult GetTargetHours(ShiftDto shift, DateTime? from = null, DateTime? to = null)
        {
            try
            {
                var result = _shiftService.GetTargetHours(shift, from, to);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
