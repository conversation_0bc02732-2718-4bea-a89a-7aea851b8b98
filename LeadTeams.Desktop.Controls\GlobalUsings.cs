﻿global using GMCadiomCore.Desktop.CustomControls;
global using GMCadiomCore.Desktop.Shared.Helper;
global using GMCadiomCore.Extensions.IEnumerable;
global using LeadTeams.Desktop.Controls.Controls;
global using LeadTeams.Desktop.Controls.Core.ControlsGraphics;
global using LeadTeams.Desktop.Controls.Core.Helper;
global using LeadTeams.Desktop.Controls.Core.Infrastructure;
global using LeadTeams.Desktop.Controls.Core.Interfaces;
global using LeadTeams.Desktop.Controls.LeadTeamsControls;
global using LeadTeams.Desktop.Controls.UI;
global using System.ComponentModel;
global using System.Diagnostics.CodeAnalysis;
global using System.Drawing.Drawing2D;
global using System.Drawing.Text;
global using System.Linq.Expressions;
global using System.Reflection;
global using System.Runtime.CompilerServices;
