﻿namespace LeadTeams.API.Controllers.Business
{
    public class ManagementTeamController : BaseBusinessController<ManagementTeamModel, ManagementTeamModel, CreateManagementTeamViewModel, UpdateManagementTeamViewModel>
    {
        private readonly IManagementTeamService _managementTeamService;

        public ManagementTeamController(IManagementTeamService managementTeamService) : base(managementTeamService)
        {
            _managementTeamService = managementTeamService;
        }

        [HttpGet("SelectiveTeamManagerList")]
        public IActionResult SelectiveTeamManagerList()
        {
            try
            {
                var result = _managementTeamService.SelectiveTeamManagerList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveTeamEmployeeList")]
        public IActionResult SelectiveTeamEmployeeList()
        {
            try
            {
                var result = _managementTeamService.SelectiveTeamEmployeeList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetTeamManagersForEmployee")]
        public IActionResult GetTeamManagersForEmployee(Ulid employeeId)
        {
            try
            {
                var result = _managementTeamService.GetTeamManagersForEmployee(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetTeamEmployeesForEmployee")]
        public IActionResult GetTeamEmployeesForEmployee(Ulid employeeId)
        {
            try
            {
                var result = _managementTeamService.GetTeamEmployeesForEmployee(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
