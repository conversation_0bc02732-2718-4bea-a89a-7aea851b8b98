﻿namespace LeadTeams.Client.View
{
    public class GenericAskLeaveListView : BaseListGenericView<AskLeaveModel, AskLeaveViewModel>
    {
        public GenericAskLeaveListView() : base() { }
        public GenericAskLeaveListView(IAskLeaveService askLeaveService) : base(askLeaveService, nameof(Screens.AskLeaveView)) { }
    }

    public partial class AskLeaveListView : GenericAskLeaveListView
    {
        private readonly ISession _session;

        //Constructor
        public AskLeaveListView(ISession session, IAskLeaveService askLeaveService) : base(askLeaveService)
        {
            _session = session;
            _session.AppLogger.LogInformation($"Initialize Component");
            InitializeComponent();
        }
    }
}
