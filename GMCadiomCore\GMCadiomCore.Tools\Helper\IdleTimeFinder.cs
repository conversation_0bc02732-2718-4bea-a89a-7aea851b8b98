﻿namespace GMCadiomCore.Tools.Helper
{
    internal struct LASTINPUTINFO
    {
        public uint cbSize;

        public uint dwTime;
    }

    public class IdleTimeFinder
    {
        [DllImport("User32.dll")]
        private static extern bool GetLastInputInfo(ref LASTINPUTINFO plii);

        [DllImport("Kernel32.dll")]
        private static extern uint GetLastError();

        public static uint GetIdleTime()
        {
            try
            {
                LASTINPUTINFO lastinputinfo = new LASTINPUTINFO();
                lastinputinfo.cbSize = (uint)Marshal.SizeOf(lastinputinfo);
                GetLastInputInfo(ref lastinputinfo);

                uint minTickCount = (uint)(Environment.TickCount64 & uint.MaxValue);
                uint mindwTime = lastinputinfo.dwTime & uint.MaxValue;
                uint Milliseconds = minTickCount - mindwTime & uint.MaxValue;
                uint Seconds = Milliseconds / 1000;
                return Seconds;
            }
            catch
            {
                throw new Exception(GetLastError().ToString());
            }
        }
    }
}
