﻿namespace LeadTeams.Models.ModelDTO.Reports
{
    public class PayrollReport
    {
        [Browsable(false)]
        public byte[] EmployeeId { get; set; }
        [DisplayName("Employee Name")]
        public string EmployeeName { get; set; }
        [Browsable(false)]
        public Ulid EmployeeUlid => new Ulid(EmployeeId);

        [Browsable(false)]
        public byte[] OrganizationId { get; set; }
        [Browsable(false)]
        public Ulid OrganizationUlid => new Ulid(OrganizationId);

        [Browsable(false)]
        [DisplayName("Duration")]
        [Column(TypeName = "Time"), DataType(DataType.Time)]
        public TimeSpan AttendanceLogDuration { get; set; }
        [DisplayName("Attendance Log Duration")]
        public string AttendanceLogDurationString => AttendanceLogDuration.FormatTimeSpanToString();
        [Browsable(false)]
        [DisplayName("Target Hours")]
        [Column(TypeName = "Time"), DataType(DataType.Time)]
        public TimeSpan TargetHours { get; set; }
        [Browsable(false)]
        [DisplayName("Target Hours")]
        public string TargetHoursString => TargetHours.FormatTimeSpanToString();
        [Browsable(false)]
        [DisplayName("Working Duration")]
        public string WorkingDuration => $"{AttendanceLogDurationString} / {TargetHoursString}";

        [DisplayName("Salary")]
        public decimal Salary { get; set; }
        [DisplayName("Allowance Amount")]
        public decimal AllowanceAmount { get; set; }
        [DisplayName("Attendance Log Amount")]
        public decimal AttendanceLogAmount { get; set; }
        [DisplayName("Net Amount")]
        public decimal NetAmount => AttendanceLogAmount + AllowanceAmount;
        [DisplayName("Currency")]
        public string? Currency { get; set; }
    }
}
