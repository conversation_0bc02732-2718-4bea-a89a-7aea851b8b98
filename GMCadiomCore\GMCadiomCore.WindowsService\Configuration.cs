﻿namespace GMCadiomCore.WindowsService
{
    public class Configuration
    {
        public static string ServiceName { get; set; } = "LeadTeamsClientService";
        public static string ServiceDescription { get; set; } = "LeadTeams Client Service";
        public static string ServiceFileName { get; set; } = "LeadTeams.Client.WindowsService.exe";
        public static string DisplayName { get; set; } = "LeadTeams Client Service";
        static string ServiceFilePath
        {
            get
            {
                string? appPath = Path.GetDirectoryName(Assembly.GetEntryAssembly()?.Location);
                if (appPath != null)
                {
                    string serviceFilePath = Path.Combine(appPath, ServiceFileName);
                    return serviceFilePath;
                }
                return string.Empty;
            }
        }

        public static bool IsInstalled()
        {
            bool isInstalled = ServiceInstaller.ServiceIsInstalled(ServiceName);
            return isInstalled;
        }

        public static bool IsRunning()
        {
            bool IsRunning = ServiceInstaller.GetServiceStatus(ServiceName) == ServiceState.Starting;
            return IsRunning;
        }

        public static void InstallService()
        {
            ServiceInstaller.Install(
                ServiceName,
                ServiceDescription,
                DisplayName,
                ServiceFilePath,
                null, null, ServiceBootFlag.AutoStart);

        }

        public static void UninstallService()
        {
            if (ServiceInstaller.GetServiceStatus(ServiceName) != ServiceState.Stop)
                ServiceInstaller.StopService(ServiceName);

            while (ServiceInstaller.GetServiceStatus(ServiceName) != ServiceState.Stop)
                Thread.Sleep(400);

            ServiceInstaller.Uninstall(ServiceName);
        }

        public static void StartService()
        {
            try
            {
                if (ServiceInstaller.GetServiceStatus(ServiceName) != ServiceState.Starting)
                    ServiceInstaller.StartService(ServiceName);
            }
            catch (Exception e)
            {
                ReStartService();
            }
        }

        public static void ReStartService()
        {
            if (ServiceInstaller.ServiceIsInstalled(ServiceName) &&
            ServiceInstaller.GetServiceStatus(ServiceName) != ServiceState.Starting)
            {
                ServiceInstaller.StopService(ServiceName);
                while (ServiceInstaller.GetServiceStatus(ServiceName) == ServiceState.Starting)
                    Thread.Sleep(200);

                ServiceInstaller.StartService(ServiceName);
                while (ServiceInstaller.GetServiceStatus(ServiceName) != ServiceState.Starting)
                    Thread.Sleep(200);
            }
        }
    }
}
