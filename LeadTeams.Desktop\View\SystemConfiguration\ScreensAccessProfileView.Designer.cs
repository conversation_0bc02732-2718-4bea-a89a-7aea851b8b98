﻿namespace LeadTeams.Desktop.View
{
    partial class ScreensAccessProfileView
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new Container();
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            gbxMainData = new LeadTeamsGroupBox();
            tlpMainData = new LeadTeamsTableLayoutPanel();
            txtScreensAccessProfileName = new LeadTeamsLabeledTextBox();
            dgvScreensAccessProfileDetails = new LeadTeamsDataGridView();
            idDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            screenIdDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            screenTextDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            canShowDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            canOpenDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            canAddDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            canEditDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            canDeleteDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            canPrintDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            selectAllDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            indexScreensAccessProfileDetailsViewModelBindingSource = new BindingSource(components);
            leadTeamsTableLayoutPanel1 = new LeadTeamsTableLayoutPanel();
            cbxSelectAll = new CheckBox();
            cbxSelectAllCanShow = new CheckBox();
            cbxSelectAllCanOpen = new CheckBox();
            cbxSelectAllCanAdd = new CheckBox();
            cbxSelectAllCanEdit = new CheckBox();
            cbxSelectAllCanDelete = new CheckBox();
            cbxSelectAllCanPrint = new CheckBox();
            gbxMainData.SuspendLayout();
            tlpMainData.SuspendLayout();
            ((ISupportInitialize)dgvScreensAccessProfileDetails).BeginInit();
            ((ISupportInitialize)indexScreensAccessProfileDetailsViewModelBindingSource).BeginInit();
            leadTeamsTableLayoutPanel1.SuspendLayout();
            SuspendLayout();
            // 
            // gbxMainData
            // 
            gbxMainData.BackColor = Color.FromArgb(234, 242, 248);
            gbxMainData.Controls.Add(tlpMainData);
            gbxMainData.Dock = DockStyle.Fill;
            gbxMainData.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            gbxMainData.ForeColor = Color.FromArgb(22, 71, 117);
            gbxMainData.Location = new Point(0, 40);
            gbxMainData.Margin = new Padding(4, 5, 4, 5);
            gbxMainData.Name = "gbxMainData";
            gbxMainData.Padding = new Padding(4, 5, 4, 5);
            gbxMainData.PaletteProperties.CustomBackColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Default;
            gbxMainData.PaletteProperties.CustomFont = Desktop.Controls.Core.Helper.Enums.FontsList.Font1;
            gbxMainData.PaletteProperties.CustomForeColor = Desktop.Controls.Core.Helper.Enums.ColorsList.Color1;
            gbxMainData.RightToLeft = RightToLeft.No;
            gbxMainData.Size = new Size(784, 521);
            gbxMainData.TabIndex = 2;
            gbxMainData.TabStop = false;
            gbxMainData.Text = "Main Data";
            // 
            // tlpMainData
            // 
            tlpMainData.BackColor = Color.FromArgb(234, 242, 248);
            tlpMainData.ColumnCount = 3;
            tlpMainData.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 250F));
            tlpMainData.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 500F));
            tlpMainData.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tlpMainData.Controls.Add(txtScreensAccessProfileName, 0, 0);
            tlpMainData.Controls.Add(dgvScreensAccessProfileDetails, 0, 2);
            tlpMainData.Controls.Add(leadTeamsTableLayoutPanel1, 0, 1);
            tlpMainData.Dock = DockStyle.Fill;
            tlpMainData.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            tlpMainData.ForeColor = Color.FromArgb(22, 71, 117);
            tlpMainData.Location = new Point(4, 26);
            tlpMainData.Margin = new Padding(4, 5, 4, 5);
            tlpMainData.Name = "tlpMainData";
            tlpMainData.RightToLeft = RightToLeft.No;
            tlpMainData.RowCount = 3;
            tlpMainData.RowStyles.Add(new RowStyle(SizeType.Absolute, 50F));
            tlpMainData.RowStyles.Add(new RowStyle(SizeType.Absolute, 50F));
            tlpMainData.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlpMainData.Size = new Size(776, 490);
            tlpMainData.TabIndex = 0;
            // 
            // txtScreensAccessProfileName
            // 
            tlpMainData.SetColumnSpan(txtScreensAccessProfileName, 2);
            txtScreensAccessProfileName.Dock = DockStyle.Fill;
            txtScreensAccessProfileName.LabelValue = "Profile Name";
            txtScreensAccessProfileName.Location = new Point(4, 5);
            txtScreensAccessProfileName.Margin = new Padding(4, 5, 4, 5);
            txtScreensAccessProfileName.Name = "txtScreensAccessProfileName";
            txtScreensAccessProfileName.Size = new Size(742, 40);
            txtScreensAccessProfileName.TabIndex = 0;
            // 
            // dgvScreensAccessProfileDetails
            // 
            dgvScreensAccessProfileDetails.AllowUserToAddRows = false;
            dgvScreensAccessProfileDetails.AllowUserToDeleteRows = false;
            dgvScreensAccessProfileDetails.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(163, 225, 249);
            dataGridViewCellStyle1.Font = new Font("Segoe UI", 10F);
            dataGridViewCellStyle1.ForeColor = Color.FromArgb(14, 53, 88);
            dataGridViewCellStyle1.SelectionBackColor = Color.FromArgb(39, 170, 224);
            dataGridViewCellStyle1.SelectionForeColor = Color.FromArgb(14, 53, 88);
            dgvScreensAccessProfileDetails.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            dgvScreensAccessProfileDetails.AutoGenerateColumns = false;
            dgvScreensAccessProfileDetails.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvScreensAccessProfileDetails.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dgvScreensAccessProfileDetails.BackgroundColor = Color.FromArgb(234, 242, 248);
            dgvScreensAccessProfileDetails.BorderStyle = BorderStyle.None;
            dgvScreensAccessProfileDetails.CellBorderStyle = DataGridViewCellBorderStyle.SunkenHorizontal;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = Color.FromArgb(22, 71, 117);
            dataGridViewCellStyle2.Font = new Font("Segoe UI", 10F);
            dataGridViewCellStyle2.ForeColor = Color.FromArgb(234, 242, 248);
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(43, 58, 103);
            dataGridViewCellStyle2.SelectionForeColor = Color.FromArgb(234, 242, 248);
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            dgvScreensAccessProfileDetails.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            dgvScreensAccessProfileDetails.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvScreensAccessProfileDetails.Columns.AddRange(new DataGridViewColumn[] { idDataGridViewTextBoxColumn, screenIdDataGridViewTextBoxColumn, screenTextDataGridViewTextBoxColumn, canShowDataGridViewCheckBoxColumn, canOpenDataGridViewCheckBoxColumn, canAddDataGridViewCheckBoxColumn, canEditDataGridViewCheckBoxColumn, canDeleteDataGridViewCheckBoxColumn, canPrintDataGridViewCheckBoxColumn, selectAllDataGridViewCheckBoxColumn });
            tlpMainData.SetColumnSpan(dgvScreensAccessProfileDetails, 3);
            dgvScreensAccessProfileDetails.DataSource = indexScreensAccessProfileDetailsViewModelBindingSource;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = Color.FromArgb(163, 225, 249);
            dataGridViewCellStyle3.Font = new Font("Segoe UI", 10F);
            dataGridViewCellStyle3.ForeColor = Color.FromArgb(14, 53, 88);
            dataGridViewCellStyle3.SelectionBackColor = Color.FromArgb(39, 170, 224);
            dataGridViewCellStyle3.SelectionForeColor = Color.FromArgb(14, 53, 88);
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.False;
            dgvScreensAccessProfileDetails.DefaultCellStyle = dataGridViewCellStyle3;
            dgvScreensAccessProfileDetails.Dock = DockStyle.Fill;
            dgvScreensAccessProfileDetails.EnableHeadersVisualStyles = false;
            dgvScreensAccessProfileDetails.Font = new Font("Segoe UI", 10F);
            dgvScreensAccessProfileDetails.Location = new Point(4, 105);
            dgvScreensAccessProfileDetails.Margin = new Padding(4, 5, 4, 5);
            dgvScreensAccessProfileDetails.MultiSelect = false;
            dgvScreensAccessProfileDetails.Name = "dgvScreensAccessProfileDetails";
            dgvScreensAccessProfileDetails.RightToLeft = RightToLeft.No;
            dgvScreensAccessProfileDetails.RowHeadersVisible = false;
            dgvScreensAccessProfileDetails.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            dgvScreensAccessProfileDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvScreensAccessProfileDetails.Size = new Size(768, 380);
            dgvScreensAccessProfileDetails.TabIndex = 3;
            dgvScreensAccessProfileDetails.Tag = "Needed";
            // 
            // idDataGridViewTextBoxColumn
            // 
            idDataGridViewTextBoxColumn.DataPropertyName = "Id";
            idDataGridViewTextBoxColumn.HeaderText = "Id";
            idDataGridViewTextBoxColumn.Name = "idDataGridViewTextBoxColumn";
            idDataGridViewTextBoxColumn.ReadOnly = true;
            idDataGridViewTextBoxColumn.Visible = false;
            // 
            // screenIdDataGridViewTextBoxColumn
            // 
            screenIdDataGridViewTextBoxColumn.DataPropertyName = "ScreenId";
            screenIdDataGridViewTextBoxColumn.HeaderText = "Screen Id";
            screenIdDataGridViewTextBoxColumn.Name = "screenIdDataGridViewTextBoxColumn";
            screenIdDataGridViewTextBoxColumn.ReadOnly = true;
            screenIdDataGridViewTextBoxColumn.Visible = false;
            // 
            // screenTextDataGridViewTextBoxColumn
            // 
            screenTextDataGridViewTextBoxColumn.DataPropertyName = "ScreenText";
            screenTextDataGridViewTextBoxColumn.HeaderText = "Screen Text";
            screenTextDataGridViewTextBoxColumn.Name = "screenTextDataGridViewTextBoxColumn";
            screenTextDataGridViewTextBoxColumn.ReadOnly = true;
            // 
            // canShowDataGridViewCheckBoxColumn
            // 
            canShowDataGridViewCheckBoxColumn.DataPropertyName = "CanShow";
            canShowDataGridViewCheckBoxColumn.HeaderText = "Can Show";
            canShowDataGridViewCheckBoxColumn.Name = "canShowDataGridViewCheckBoxColumn";
            // 
            // canOpenDataGridViewCheckBoxColumn
            // 
            canOpenDataGridViewCheckBoxColumn.DataPropertyName = "CanOpen";
            canOpenDataGridViewCheckBoxColumn.HeaderText = "Can Open";
            canOpenDataGridViewCheckBoxColumn.Name = "canOpenDataGridViewCheckBoxColumn";
            // 
            // canAddDataGridViewCheckBoxColumn
            // 
            canAddDataGridViewCheckBoxColumn.DataPropertyName = "CanAdd";
            canAddDataGridViewCheckBoxColumn.HeaderText = "Can Add";
            canAddDataGridViewCheckBoxColumn.Name = "canAddDataGridViewCheckBoxColumn";
            // 
            // canEditDataGridViewCheckBoxColumn
            // 
            canEditDataGridViewCheckBoxColumn.DataPropertyName = "CanEdit";
            canEditDataGridViewCheckBoxColumn.HeaderText = "Can Edit";
            canEditDataGridViewCheckBoxColumn.Name = "canEditDataGridViewCheckBoxColumn";
            // 
            // canDeleteDataGridViewCheckBoxColumn
            // 
            canDeleteDataGridViewCheckBoxColumn.DataPropertyName = "CanDelete";
            canDeleteDataGridViewCheckBoxColumn.HeaderText = "Can Delete";
            canDeleteDataGridViewCheckBoxColumn.Name = "canDeleteDataGridViewCheckBoxColumn";
            // 
            // canPrintDataGridViewCheckBoxColumn
            // 
            canPrintDataGridViewCheckBoxColumn.DataPropertyName = "CanPrint";
            canPrintDataGridViewCheckBoxColumn.HeaderText = "Can Print";
            canPrintDataGridViewCheckBoxColumn.Name = "canPrintDataGridViewCheckBoxColumn";
            // 
            // selectAllDataGridViewCheckBoxColumn
            // 
            selectAllDataGridViewCheckBoxColumn.DataPropertyName = "SelectAll";
            selectAllDataGridViewCheckBoxColumn.HeaderText = "Select All";
            selectAllDataGridViewCheckBoxColumn.Name = "selectAllDataGridViewCheckBoxColumn";
            // 
            // indexScreensAccessProfileDetailsViewModelBindingSource
            // 
            indexScreensAccessProfileDetailsViewModelBindingSource.DataSource = typeof(IndexScreensAccessProfileDetailsViewModel);
            // 
            // leadTeamsTableLayoutPanel1
            // 
            leadTeamsTableLayoutPanel1.BackColor = Color.FromArgb(234, 242, 248);
            leadTeamsTableLayoutPanel1.ColumnCount = 8;
            tlpMainData.SetColumnSpan(leadTeamsTableLayoutPanel1, 3);
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 12.5F));
            leadTeamsTableLayoutPanel1.Controls.Add(cbxSelectAll, 7, 0);
            leadTeamsTableLayoutPanel1.Controls.Add(cbxSelectAllCanShow, 1, 0);
            leadTeamsTableLayoutPanel1.Controls.Add(cbxSelectAllCanOpen, 2, 0);
            leadTeamsTableLayoutPanel1.Controls.Add(cbxSelectAllCanAdd, 3, 0);
            leadTeamsTableLayoutPanel1.Controls.Add(cbxSelectAllCanEdit, 4, 0);
            leadTeamsTableLayoutPanel1.Controls.Add(cbxSelectAllCanDelete, 5, 0);
            leadTeamsTableLayoutPanel1.Controls.Add(cbxSelectAllCanPrint, 6, 0);
            leadTeamsTableLayoutPanel1.Dock = DockStyle.Fill;
            leadTeamsTableLayoutPanel1.ForeColor = Color.FromArgb(22, 71, 117);
            leadTeamsTableLayoutPanel1.Location = new Point(3, 53);
            leadTeamsTableLayoutPanel1.Name = "leadTeamsTableLayoutPanel1";
            leadTeamsTableLayoutPanel1.RightToLeft = RightToLeft.No;
            leadTeamsTableLayoutPanel1.RowCount = 1;
            leadTeamsTableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            leadTeamsTableLayoutPanel1.Size = new Size(770, 44);
            leadTeamsTableLayoutPanel1.TabIndex = 4;
            // 
            // cbxSelectAll
            // 
            cbxSelectAll.AutoSize = true;
            cbxSelectAll.BackColor = Color.FromArgb(234, 242, 248);
            cbxSelectAll.CheckAlign = ContentAlignment.TopCenter;
            cbxSelectAll.Dock = DockStyle.Fill;
            cbxSelectAll.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSelectAll.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSelectAll.Location = new Point(675, 3);
            cbxSelectAll.Name = "cbxSelectAll";
            cbxSelectAll.RightToLeft = RightToLeft.No;
            cbxSelectAll.Size = new Size(92, 38);
            cbxSelectAll.TabIndex = 1;
            cbxSelectAll.Text = "SelectAll";
            cbxSelectAll.TextAlign = ContentAlignment.TopCenter;
            cbxSelectAll.TextImageRelation = TextImageRelation.TextBeforeImage;
            cbxSelectAll.UseVisualStyleBackColor = false;
            // 
            // cbxSelectAllCanShow
            // 
            cbxSelectAllCanShow.AutoSize = true;
            cbxSelectAllCanShow.BackColor = Color.FromArgb(234, 242, 248);
            cbxSelectAllCanShow.CheckAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanShow.Dock = DockStyle.Fill;
            cbxSelectAllCanShow.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSelectAllCanShow.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSelectAllCanShow.Location = new Point(99, 3);
            cbxSelectAllCanShow.Name = "cbxSelectAllCanShow";
            cbxSelectAllCanShow.RightToLeft = RightToLeft.No;
            cbxSelectAllCanShow.Size = new Size(90, 38);
            cbxSelectAllCanShow.TabIndex = 0;
            cbxSelectAllCanShow.Text = "SelectAll";
            cbxSelectAllCanShow.TextAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanShow.TextImageRelation = TextImageRelation.TextBeforeImage;
            cbxSelectAllCanShow.UseVisualStyleBackColor = false;
            // 
            // cbxSelectAllCanOpen
            // 
            cbxSelectAllCanOpen.AutoSize = true;
            cbxSelectAllCanOpen.BackColor = Color.FromArgb(234, 242, 248);
            cbxSelectAllCanOpen.CheckAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanOpen.Dock = DockStyle.Fill;
            cbxSelectAllCanOpen.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSelectAllCanOpen.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSelectAllCanOpen.Location = new Point(195, 3);
            cbxSelectAllCanOpen.Name = "cbxSelectAllCanOpen";
            cbxSelectAllCanOpen.RightToLeft = RightToLeft.No;
            cbxSelectAllCanOpen.Size = new Size(90, 38);
            cbxSelectAllCanOpen.TabIndex = 0;
            cbxSelectAllCanOpen.Text = "SelectAll";
            cbxSelectAllCanOpen.TextAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanOpen.TextImageRelation = TextImageRelation.TextBeforeImage;
            cbxSelectAllCanOpen.UseVisualStyleBackColor = false;
            // 
            // cbxSelectAllCanAdd
            // 
            cbxSelectAllCanAdd.AutoSize = true;
            cbxSelectAllCanAdd.BackColor = Color.FromArgb(234, 242, 248);
            cbxSelectAllCanAdd.CheckAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanAdd.Dock = DockStyle.Fill;
            cbxSelectAllCanAdd.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSelectAllCanAdd.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSelectAllCanAdd.Location = new Point(291, 3);
            cbxSelectAllCanAdd.Name = "cbxSelectAllCanAdd";
            cbxSelectAllCanAdd.RightToLeft = RightToLeft.No;
            cbxSelectAllCanAdd.Size = new Size(90, 38);
            cbxSelectAllCanAdd.TabIndex = 0;
            cbxSelectAllCanAdd.Text = "SelectAll";
            cbxSelectAllCanAdd.TextAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanAdd.TextImageRelation = TextImageRelation.TextBeforeImage;
            cbxSelectAllCanAdd.UseVisualStyleBackColor = false;
            // 
            // cbxSelectAllCanEdit
            // 
            cbxSelectAllCanEdit.AutoSize = true;
            cbxSelectAllCanEdit.BackColor = Color.FromArgb(234, 242, 248);
            cbxSelectAllCanEdit.CheckAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanEdit.Dock = DockStyle.Fill;
            cbxSelectAllCanEdit.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSelectAllCanEdit.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSelectAllCanEdit.Location = new Point(387, 3);
            cbxSelectAllCanEdit.Name = "cbxSelectAllCanEdit";
            cbxSelectAllCanEdit.RightToLeft = RightToLeft.No;
            cbxSelectAllCanEdit.Size = new Size(90, 38);
            cbxSelectAllCanEdit.TabIndex = 0;
            cbxSelectAllCanEdit.Text = "SelectAll";
            cbxSelectAllCanEdit.TextAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanEdit.TextImageRelation = TextImageRelation.TextBeforeImage;
            cbxSelectAllCanEdit.UseVisualStyleBackColor = false;
            // 
            // cbxSelectAllCanDelete
            // 
            cbxSelectAllCanDelete.AutoSize = true;
            cbxSelectAllCanDelete.BackColor = Color.FromArgb(234, 242, 248);
            cbxSelectAllCanDelete.CheckAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanDelete.Dock = DockStyle.Fill;
            cbxSelectAllCanDelete.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSelectAllCanDelete.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSelectAllCanDelete.Location = new Point(483, 3);
            cbxSelectAllCanDelete.Name = "cbxSelectAllCanDelete";
            cbxSelectAllCanDelete.RightToLeft = RightToLeft.No;
            cbxSelectAllCanDelete.Size = new Size(90, 38);
            cbxSelectAllCanDelete.TabIndex = 0;
            cbxSelectAllCanDelete.Text = "SelectAll";
            cbxSelectAllCanDelete.TextAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanDelete.TextImageRelation = TextImageRelation.TextBeforeImage;
            cbxSelectAllCanDelete.UseVisualStyleBackColor = false;
            // 
            // cbxSelectAllCanPrint
            // 
            cbxSelectAllCanPrint.AutoSize = true;
            cbxSelectAllCanPrint.BackColor = Color.FromArgb(234, 242, 248);
            cbxSelectAllCanPrint.CheckAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanPrint.Dock = DockStyle.Fill;
            cbxSelectAllCanPrint.Font = new Font("Novus-Regular", 11F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cbxSelectAllCanPrint.ForeColor = Color.FromArgb(22, 71, 117);
            cbxSelectAllCanPrint.Location = new Point(579, 3);
            cbxSelectAllCanPrint.Name = "cbxSelectAllCanPrint";
            cbxSelectAllCanPrint.RightToLeft = RightToLeft.No;
            cbxSelectAllCanPrint.Size = new Size(90, 38);
            cbxSelectAllCanPrint.TabIndex = 0;
            cbxSelectAllCanPrint.Text = "SelectAll";
            cbxSelectAllCanPrint.TextAlign = ContentAlignment.TopCenter;
            cbxSelectAllCanPrint.TextImageRelation = TextImageRelation.TextBeforeImage;
            cbxSelectAllCanPrint.UseVisualStyleBackColor = false;
            // 
            // ScreensAccessProfileView
            // 
            AutoScaleDimensions = new SizeF(9F, 21F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(784, 561);
            Controls.Add(gbxMainData);
            Margin = new Padding(6, 8, 6, 8);
            Name = "ScreensAccessProfileView";
            Text = "ScreensAccessProfileView";
            Controls.SetChildIndex(gbxMainData, 0);
            gbxMainData.ResumeLayout(false);
            tlpMainData.ResumeLayout(false);
            ((ISupportInitialize)dgvScreensAccessProfileDetails).EndInit();
            ((ISupportInitialize)indexScreensAccessProfileDetailsViewModelBindingSource).EndInit();
            leadTeamsTableLayoutPanel1.ResumeLayout(false);
            leadTeamsTableLayoutPanel1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private LeadTeamsGroupBox gbxMainData;
        private LeadTeamsTableLayoutPanel tlpMainData;
        private LeadTeamsLabeledTextBox txtScreensAccessProfileName;
        private LeadTeamsDataGridView dgvScreensAccessProfileDetails;
        private DataGridViewTextBoxColumn ScreenIdCol;
        private LeadTeamsTableLayoutPanel leadTeamsTableLayoutPanel1;
        private CheckBox cbxSelectAllCanShow;
        private CheckBox cbxSelectAllCanOpen;
        private CheckBox cbxSelectAllCanAdd;
        private CheckBox cbxSelectAllCanEdit;
        private CheckBox cbxSelectAllCanDelete;
        private CheckBox cbxSelectAllCanPrint;
        private CheckBox cbxSelectAll;
        private BindingSource indexScreensAccessProfileDetailsViewModelBindingSource;
        private DataGridViewTextBoxColumn idDataGridViewTextBoxColumn;
        private DataGridViewTextBoxColumn screenIdDataGridViewTextBoxColumn;
        private DataGridViewTextBoxColumn screenTextDataGridViewTextBoxColumn;
        private DataGridViewCheckBoxColumn canShowDataGridViewCheckBoxColumn;
        private DataGridViewCheckBoxColumn canOpenDataGridViewCheckBoxColumn;
        private DataGridViewCheckBoxColumn canAddDataGridViewCheckBoxColumn;
        private DataGridViewCheckBoxColumn canEditDataGridViewCheckBoxColumn;
        private DataGridViewCheckBoxColumn canDeleteDataGridViewCheckBoxColumn;
        private DataGridViewCheckBoxColumn canPrintDataGridViewCheckBoxColumn;
        private DataGridViewCheckBoxColumn selectAllDataGridViewCheckBoxColumn;
    }
}