﻿namespace GMCadiomCore.Tools.Helper
{
    public static class Logger
    {
        public static ILogger CreateSerilog(string LogPath)
        {
            return new LoggerConfiguration()
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
                .Enrich.FromLogContext()
                .WriteTo.File(LogPath, rollingInterval: RollingInterval.Day)
                .CreateLogger();
        }
    }
}
