﻿namespace GMCadiomCore.Services.API
{
    public enum HttpMethodEnum
    {
        GET,
        POST,
        PUT,
        DELETE,
        PATCH,
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class HttpRequestMethodAttribute : Attribute
    {
        public string MethodName { get; }
        public HttpMethodEnum HttpMethod { get; }
        public bool UseBody { get; }

        public HttpRequestMethodAttribute(string methodName, HttpMethodEnum httpMethod, bool useBody = false)
        {
            MethodName = methodName;
            HttpMethod = httpMethod;
            UseBody = useBody;
        }
    }
}
