﻿namespace LeadTeams.Desktop.Controls.UI
{
    public class DataGridViewUI
    {
        public DataGridViewUI(DataGridView obj)
        {
            Font font = new Font("Segoe UI", 10, FontStyle.Regular);

            obj.Font = font;
            obj.AlternatingRowsDefaultCellStyle.Font = font;
            obj.ColumnHeadersDefaultCellStyle.Font = font;
            obj.DefaultCellStyle.Font = font;

            obj.RightToLeft = ColorsSchema.IsRTL;

            obj.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            obj.AllowUserToDeleteRows = false;
            obj.AllowUserToOrderColumns = false;
            obj.AllowUserToResizeColumns = true;
            obj.AllowUserToResizeRows = false;
            obj.EnableHeadersVisualStyles = false;
            obj.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            obj.RowHeadersVisible = false;
            obj.MultiSelect = false;
            obj.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            obj.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            obj.BorderStyle = BorderStyle.None;
            obj.CellBorderStyle = DataGridViewCellBorderStyle.SunkenHorizontal;

            obj.BackColor = ColorsSchema.DataGridView.BackColor;
            obj.ForeColor = ColorsSchema.DataGridView.ForeColor;
            obj.BackgroundColor = ColorsSchema.DataGridView.BackgroundColor;

            obj.AlternatingRowsDefaultCellStyle.BackColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleBackColor;
            obj.AlternatingRowsDefaultCellStyle.ForeColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleForeColor;
            obj.AlternatingRowsDefaultCellStyle.SelectionBackColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleSelectionBackColor;
            obj.AlternatingRowsDefaultCellStyle.SelectionForeColor = ColorsSchema.DataGridView.AlternatingRowsDefaultCellStyleSelectionForeColor;

            obj.ColumnHeadersDefaultCellStyle.BackColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleBackColor; // خلفيه خلايا الهيدير
            obj.ColumnHeadersDefaultCellStyle.ForeColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleForeColor; // خط خلايا الهيدير
            obj.ColumnHeadersDefaultCellStyle.SelectionBackColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleSelectionBackColor; // خط خلايا الهيدير
            obj.ColumnHeadersDefaultCellStyle.SelectionForeColor = ColorsSchema.DataGridView.ColumnHeadersDefaultCellStyleSelectionForeColor; // خط خلايا الهيدير

            obj.DefaultCellStyle.BackColor = ColorsSchema.DataGridView.DefaultCellStyleBackColor; // لون الخط بداخل الجدول
            obj.DefaultCellStyle.ForeColor = ColorsSchema.DataGridView.DefaultCellStyleForeColor; // لون الخط بداخل الجدول
            obj.DefaultCellStyle.SelectionBackColor = ColorsSchema.DataGridView.DefaultCellStyleSelectionBackColor; // لون الخلفيه للخلايا المحدده
            obj.DefaultCellStyle.SelectionForeColor = ColorsSchema.DataGridView.DefaultCellStyleSelectionForeColor; // لون الخط للخلايا المحدده

            obj.EnableHeadersVisualStyles = false;

            foreach (DataGridViewColumn col in obj.Columns)
                col.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;

            obj.AllowUserToAddRows = false;
            if (obj.Tag?.ToString() != "Needed")
            {
                obj.ReadOnly = true;
                obj.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            }
            else
            {
                obj.ReadOnly = false;
                obj.SelectionMode = DataGridViewSelectionMode.RowHeaderSelect;
            }
        }
    }
}
