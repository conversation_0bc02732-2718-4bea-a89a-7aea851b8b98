﻿global using GMCadiomCore.Authentications.PermissionsAndSessions;
global using GMCadiomCore.Desktop.Extensions;
global using GMCadiomCore.Desktop.SyncTools;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.ModelValidation.Base;
global using GMCadiomCore.Models.ResultPattern;
global using GMCadiomCore.Shared.Extensions;
global using GMCadiomCore.Shared.Helper;
global using GMCadiomCore.Streaming;
global using GMCadiomCore.Tools.Helper;
global using LeadTeams.Client.DI;
global using LeadTeams.Client.Handlers;
global using LeadTeams.Client.Popups;
global using LeadTeams.Client.Services;
global using LeadTeams.Client.View;
global using LeadTeams.DbSync;
global using LeadTeams.Desktop.Controls.CombinedControls;
global using LeadTeams.Desktop.Controls.LeadTeamsControls;
global using LeadTeams.Desktop.Controls.Utilities;
global using LeadTeams.Desktop.Services.Authentication;
global using LeadTeams.Desktop.Services.BaseView;
global using LeadTeams.Integration.SignalRChat.DI;
global using LeadTeams.Integration.SignalRChat.SubscribeSignalR;
global using LeadTeams.Models.Enumerations;
global using LeadTeams.Models.Model;
global using LeadTeams.Models.ModelDTO;
global using LeadTeams.Models.ModelDTO.Authentication;
global using LeadTeams.Models.ModelMapper;
global using LeadTeams.Models.ViewModel.AskLeave;
global using LeadTeams.Models.ViewModel.AttendanceLog;
global using LeadTeams.Models.ViewModel.Meeting;
global using LeadTeams.Models.ViewModel.ScreenShotsMonitoring;
global using LeadTeams.PermissionAndSession;
global using LeadTeams.PermissionAndSession.Authentication;
global using LeadTeams.PermissionAndSession.Session;
global using LeadTeams.Services.API.DI;
global using LeadTeams.Services.Core.Authentication;
global using LeadTeams.Services.Core.Business;
global using LeadTeams.Shared.Helper;
global using LeadTeams.Shared.Windows.CustomControls.Message;
global using LeadTeams.Shared.Windows.Helper;
global using Microsoft.Extensions.DependencyInjection;
global using SignalRChat.Core.Infrastructure.ConnectionManager;
global using SignalRChat.Core.Modules.Login.Services;
global using SignalRChat.DI;
global using System.ComponentModel;
global using System.Diagnostics;
global using System.Drawing.Imaging;
global using System.Reflection;
global using System.Runtime.CompilerServices;
