﻿namespace LeadTeams.Repositories.EF.Factory
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private ApplicationDBContext _context;

        public UnitOfWork(IDatabaseService databaseService)
        {
            _context = databaseService.GetDbContext();
            Allowance = new AllowanceRepository(_context);
            AskLeave = new AskLeaveRepository(_context);
            AttendanceLog = new AttendanceLogRepository(_context);
            Audit = new AuditRepository(_context);
            EmployeeAllowance = new EmployeeAllowanceRepository(_context);
            EmployeeEducationalQualification = new EmployeeEducationalQualificationRepository(_context);
            EmployeeKids = new EmployeeKidsRepository(_context);
            EmployeeManager = new EmployeeManagerRepository(_context);
            Employee = new EmployeeRepository(_context);
            ManagementTeamEmployee = new ManagementTeamEmployeeRepository(_context);
            ManagementTeam = new ManagementTeamRepository(_context);
            ManagementTeamManager = new ManagementTeamManagerRepository(_context);
            MeetingEmployees = new MeetingEmployeeRepository(_context);
            Meeting = new MeetingRepository(_context);
            Message = new MessageRepository(_context);
            Organization = new OrganizationRepository(_context);
            OrganizationNews = new OrganizationNewsRepository(_context);
            Project = new ProjectRepository(_context);
            RefreshToken = new RefreshTokenRepository(_context);
            ScreensAccessProfile = new ScreensAccessProfileRepository(_context);
            ScreensAccessProfileDetails = new ScreensAccessProfileDetailsRepository(_context);
            ScreenShotsMonitoring = new ScreenShotsMonitoringRepository(_context);
            Shift = new ShiftRepository(_context);
            ShiftDynamicPattern = new ShiftDynamicPatternRepository(_context);
            ShiftFixedPattern = new ShiftFixedPatternRepository(_context);
            Setting = new SettingRepository(_context);
            Task = new TaskRepository(_context);
            User = new UserRepository(_context);
        }

        public IBaseRepository<AllowanceModel, AllowanceModel> Allowance { get; private set; }
        public IBaseRepository<AskLeaveModel, AskLeaveViewModel> AskLeave { get; private set; }
        public IAttendanceLogRepository<AttendanceLogModel, AttendanceLogViewModel> AttendanceLog { get; private set; }
        public IAuditRepository<AuditModel> Audit { get; private set; }
        public IEmployeeAllowanceRepository<EmployeeAllowanceModel, EmployeeAllowanceModel> EmployeeAllowance { get; private set; }
        public IEmployeeEducationalQualificationRepository<EmployeeEducationalQualificationModel, EmployeeEducationalQualificationModel> EmployeeEducationalQualification { get; private set; }
        public IEmployeeKidsRepository<EmployeeKidsModel, EmployeeKidsModel> EmployeeKids { get; private set; }
        public IEmployeeManagerRepository<EmployeeManagerModel, EmployeeManagerModel> EmployeeManager { get; private set; }
        public IEmployeeRepository<EmployeeModel, EmployeeViewModel> Employee { get; private set; }
        public IManagementTeamEmployeeRepository<ManagementTeamEmployeeModel, ManagementTeamEmployeeModel> ManagementTeamEmployee { get; private set; }
        public IBaseRepository<ManagementTeamModel, ManagementTeamModel> ManagementTeam { get; private set; }
        public IManagementTeamManagerRepository<ManagementTeamManagerModel, ManagementTeamManagerModel> ManagementTeamManager { get; private set; }
        public IMeetingEmployeeRepository<MeetingEmployeeModel, MeetingEmployeeModel> MeetingEmployees { get; private set; }
        public IBaseRepository<MeetingModel, MeetingViewModel> Meeting { get; private set; }
        public IMessageRepository<MessageModel, MessageModel> Message { get; private set; }
        public IBaseRepository<OrganizationModel, OrganizationModel> Organization { get; private set; }
        public IBaseRepository<OrganizationNewsModel, OrganizationNewsModel> OrganizationNews { get; private set; }
        public IBaseRepository<ProjectModel, ProjectModel> Project { get; private set; }
        public IBaseRepository<ScreensAccessProfileModel, ScreensAccessProfileModel> ScreensAccessProfile { get; private set; }
        public IBaseRepository<ScreensAccessProfileDetailsModel, ScreensAccessProfileDetailsModel> ScreensAccessProfileDetails { get; private set; }
        public IRefreshTokenRepository<RefreshTokenModel, RefreshTokenModel> RefreshToken { get; private set; }
        public IScreenShotsMonitoringRepository<ScreenShotsMonitoringModel, ScreenShotsMonitoringViewModel> ScreenShotsMonitoring { get; private set; }
        public IBaseRepository<ShiftModel, ShiftModel> Shift { get; private set; }
        public IShiftDynamicPatternRepository<ShiftDynamicPatternModel, ShiftDynamicPatternModel> ShiftDynamicPattern { get; private set; }
        public IShiftFixedPatternRepository<ShiftFixedPatternModel, ShiftFixedPatternModel> ShiftFixedPattern { get; private set; }
        public IBaseRepository<SettingModel, SettingModel> Setting { get; private set; }
        public ITaskRepository<TaskModel, TaskViewModel> Task { get; private set; }
        public IUserRepository<UserModel, UserViewModel> User { get; private set; }

        public void ExecuteQuery(string Query)
        {
            if (!string.IsNullOrEmpty(Query))
            {
                FormattableString formattableString = FormattableStringFactory.Create(Query);
                _context.Database.ExecuteSql(formattableString);
            }
        }

        public void Save()
        {
            _context.SaveChanges();
        }

        private bool disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
            }
            this.disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
