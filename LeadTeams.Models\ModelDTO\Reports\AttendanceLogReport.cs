﻿namespace LeadTeams.Models.ModelDTO.Reports
{
    public class AttendanceLogReport
    {
        [Browsable(false)]
        public byte[] EmployeeId { get; set; }
        [DisplayName("Employee Name")]
        public string EmployeeName { get; set; }
        [Browsable(false)]
        public Ulid EmployeeUlid => new Ulid(EmployeeId);
        [NotMapped]
        [Browsable(false)]
        public EmployeeModel Employee { get; set; }

        [Browsable(false)]
        public byte[] OrganizationId { get; set; }

        [Browsable(false)]
        public Ulid OrganizationUlid => new Ulid(OrganizationId);
        [DisplayName("Start Date & Time")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        public DateTime StartDateTime { get; set; }
        [DisplayName("End Date & Time")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        public DateTime EndDateTime { get; set; }
        [DisplayName("Total Seconds")]
        [Browsable(false)]
        public long TotalSeconds { get; set; }

        [DisplayName("Duration")]
        public string Duration
        {
            get
            {
                var ts = TimeSpan.FromSeconds(TotalSeconds);
                return $"{ts.Hours:D2}:{ts.Minutes:D2}:{ts.Seconds:D2}";
            }
        }

        [Browsable(false)]
        [DisplayName("Employee Shift")]
        [Column(TypeName = "LongText")]
        public string AttendanceLogEmployeeShift { get; set; }
        [Browsable(false)]
        public ShiftDto? Shift => Shared.Helper.JsonUtilities.ReadFromJsonString<ShiftDto>(AttendanceLogEmployeeShift);
    }

    public class AttendanceLogByDailyReport : AttendanceLogReport
    {
    }

    public class AttendanceLogByWeeklyReport : AttendanceLogReport
    {
        [Browsable(false)]
        public uint Year { get; set; }
        [Browsable(false)]
        public uint WeekOfYear { get; set; }
    }

    public class AttendanceLogByMonthlyReport : AttendanceLogReport
    {
        [Browsable(false)]
        public uint Year { get; set; }
        [Browsable(false)]
        public uint Month { get; set; }
    }
}
