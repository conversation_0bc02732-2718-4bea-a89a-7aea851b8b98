﻿namespace LeadTeams.Desktop.View.Reports
{
    public partial class EmployeeWithPayrollLogReport : GenericViewTable.GenericEmployeePayrollLogReportView
    {
        private readonly ISession _session;
        private readonly IPayrollService _payrollService;

        public EmployeeWithPayrollLogReport(ISession session, IPayrollService payrollService)
        {
            InitializeComponent();

            _session = session;
            _payrollService = payrollService;
            DataBaseWatcherEntityName = nameof(AttendanceLogModel);

            AssociateAndRaiseEvents();

            RefreshListFunction();
        }

        private void AssociateAndRaiseEvents()
        {
            dgvMain.GetDgvTotalsSummary.DGV.DataBindingComplete += (s, e) =>
            {
                var lst = MainList.Items.ToList();

                if (!dgvMain.GetDgvTotalsSummary.DGV.Columns.Contains(RunningTotal))
                    dgvMain.GetDgvTotalsSummary.DGV.Columns.Add(RunningTotal);

                if (e.ListChangedType != ListChangedType.Reset) return;

                decimal total = 0;
                for (int i = 0; i < lst.Count; ++i)
                {
                    PayrollReport a = lst[i];
                    DataGridViewRow r = dgvMain.GetDgvTotalsSummary.DGV.Rows[i];
                    total += a.NetAmount;
                    r.Cells[nameof(RunningTotal)].Value = total;
                }
            };
        }

        public override PaginationList<PayrollReport> MainData()
        {
            var result = _payrollService.GetPayrollReports(_session.Organization.Id);
            return PaginationList<PayrollReport>.Create(result, PageNumber, PageSize);
        }

        //Methods
        protected override void AddViewListControl()
        {
            tableLayoutPanel1.Controls.Add(dgvMain, 0, 0);

            //Select Column Name To Sum
            dgvMain.GetDgvTotalsSummary.SummaryColumns = new string[] { nameof(PayrollReport.NetAmount), nameof(PayrollReport.AttendanceLogDuration) };
        }
    }
}
