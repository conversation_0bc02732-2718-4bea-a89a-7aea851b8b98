﻿namespace GMCadiomCore.Services.API
{
    public static class Utilities
    {
        private class EndPointsMap
        {
            public EndPointsMap(string controllerName, string methodName, HttpMethodEnum httpMethod, bool useBody)
            {
                ControllerName = controllerName;
                MethodName = methodName;
                HttpMethod = httpMethod;
                UseBody = useBody;
            }

            public string ControllerName { get; }
            public string MethodName { get; }
            public HttpMethodEnum HttpMethod { get; }
            public bool UseBody { get; }

            public string EndPoint => $"{ControllerName}/{MethodName}";
        }

        private static readonly ConcurrentDictionary<string, EndPointsMap> _endpointsCache = new();

        public static void Initialize(Assembly assembly)
        {
            var services = assembly.GetTypes()
                .Where(t => t.GetCustomAttribute<HttpRequestControllerAttribute>() != null)
                .ToList();

            foreach (var serviceType in services)
            {
                var controllerAttr = serviceType.GetCustomAttribute<HttpRequestControllerAttribute>();
                var genericType = serviceType.BaseType?.GetGenericArguments().FirstOrDefault();
                string controllerName = !string.IsNullOrEmpty(controllerAttr?.ControllerName)
                    ? controllerAttr.ControllerName
                    : ValidateValue.GetCleanName(genericType?.Name ?? serviceType.Name.Replace("Service", ""));

                if (string.IsNullOrEmpty(controllerName)) continue;

                foreach (var method in serviceType.GetMethods())
                {
                    var methodAttr = method.GetCustomAttribute<HttpRequestMethodAttribute>();
                    if (methodAttr == null) continue;

                    string key = $"{serviceType.FullName}.{method.Name}";
                    _endpointsCache[key] = new EndPointsMap(controllerName, methodAttr.MethodName, methodAttr.HttpMethod, methodAttr.UseBody);
                }
            }
        }

        public static async Task<string> SendRequest(this HttpClient httpClient, object caller, string? methodName, object? data = null)
        {
            string classType = caller.GetType().FullName;
            string key = $"{classType}.{methodName}";

            if (!_endpointsCache.TryGetValue(key, out var endpoint))
                throw new InvalidOperationException($"Endpoint for {key} not found in cache.");

            string url = endpoint.EndPoint;
            string fullUrl = endpoint.UseBody ? url : $"{url}{SerializeToQueryString(data)}";

            HttpResponseMessage response = endpoint.HttpMethod switch
            {
                HttpMethodEnum.GET => await httpClient.GetAsync(fullUrl),
                HttpMethodEnum.POST => await httpClient.PostAsync(fullUrl, CreateJsonContent(data)),
                HttpMethodEnum.PUT => await httpClient.PutAsync(fullUrl, CreateJsonContent(data)),
                HttpMethodEnum.PATCH => await httpClient.PatchAsync(fullUrl, CreateJsonContent(data)),
                HttpMethodEnum.DELETE => await httpClient.DeleteAsync($"{url}/{SerializeToQueryString(data, true)}"),
                _ => throw new NotSupportedException($"HTTP method {endpoint.HttpMethod} is not supported.")
            };

            return await response.Content.ReadAsStringAsync();
        }

        private static StringContent CreateJsonContent(object? data) =>
            new(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");

        private static string SerializeToQueryString(object? obj, bool isRoute = false)
        {
            if (obj == null) return string.Empty;

            var properties = obj.GetType().GetProperties();
            var queryString = string.Join("&", properties
                .Select(p =>
                {
                    var value = p.GetValue(obj);
                    if (value == null) return string.Empty;

                    return p.PropertyType == typeof(Ulid) || p.PropertyType == typeof(Ulid?)
                        ? (isRoute ? value.ToString() : $"{p.Name}={value}")
                        : $"{p.Name}={Uri.EscapeDataString(value.ToString())}";
                })
                .Where(q => !string.IsNullOrEmpty(q)));

            return queryString.Length > 0 ? (isRoute ? queryString : "?" + queryString) : string.Empty;
        }

        public static async Task<TResult?> SendRequest<TResult>(this HttpClient httpClient, object caller, object? data = null, [CallerMemberName] string? methodName = null)
        {
            try
            {
                string? responseJson = await SendRequest(httpClient, caller, methodName, data);

                // Define common serializer settings
                var serializerSettings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                };

                TResult? result = JsonConvert.DeserializeObject<TResult>(responseJson, serializerSettings);
                return result;
            }
            catch (Exception ex)
            {
                return default;
            }
        }
    }
}
