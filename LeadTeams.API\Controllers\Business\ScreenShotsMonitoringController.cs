﻿namespace LeadTeams.API.Controllers.Business
{
    public class ScreenShotsMonitoringController : BaseBusinessController<ScreenShotsMonitoringModel, ScreenShotsMonitoringViewModel, CreateScreenShotsMonitoringViewModel, UpdateScreenShotsMonitoringViewModel>
    {
        private readonly IScreenShotsMonitoringService _screenShotsMonitoringService;

        public ScreenShotsMonitoringController(IScreenShotsMonitoringService screenShotsMonitoringService) : base(screenShotsMonitoringService)
        {
            _screenShotsMonitoringService = screenShotsMonitoringService;
        }

        [HttpGet("GetLastScreenShotsMonitoring")]
        public IActionResult GetLastScreenShotsMonitoring(Ulid employeeId)
        {
            try
            {
                var result = _screenShotsMonitoringService.GetLastScreenShotsMonitoring(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
