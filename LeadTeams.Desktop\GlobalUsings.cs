﻿global using GMCadiomCore.Authentications.PermissionsAndSessions;
global using GMCadiomCore.Desktop.CustomControls.dgv;
global using GMCadiomCore.Desktop.CustomEvents;
global using GMCadiomCore.Desktop.Shared.Helper;
global using GMCadiomCore.Models.BaseModels;
global using GMCadiomCore.Models.Enumerations;
global using GMCadiomCore.Models.ViewModel;
global using GMCadiomCore.Services.Core;
global using GMCadiomCore.Shared.Helper;
global using LeadTeams.Desktop.Controls.CombinedControls;
global using LeadTeams.Desktop.Controls.LeadTeamsControls;
global using LeadTeams.Desktop.Controls.Utilities;
global using LeadTeams.Desktop.CustomControls;
global using LeadTeams.Desktop.CustomControls.dgv;
global using LeadTeams.Desktop.CustomControls.frm;
global using LeadTeams.Desktop.DI;
global using LeadTeams.Desktop.Services.Authentication;
global using LeadTeams.Desktop.Services.BaseView;
global using LeadTeams.Desktop.View;
global using LeadTeams.Desktop.View.Reports;
global using LeadTeams.Integration.SignalRChat.DI;
global using LeadTeams.Integration.SignalRChat.SubscribeSignalR;
global using LeadTeams.Models.Enumerations;
global using LeadTeams.Models.Model;
global using LeadTeams.Models.ModelDTO;
global using LeadTeams.Models.ModelDTO.Authentication;
global using LeadTeams.Models.ModelDTO.Reports;
global using LeadTeams.Models.ModelMapper;
global using LeadTeams.Models.ViewModel.Allowance;
global using LeadTeams.Models.ViewModel.AskLeave;
global using LeadTeams.Models.ViewModel.BaseModels;
global using LeadTeams.Models.ViewModel.Employee;
global using LeadTeams.Models.ViewModel.ManagementTeam;
global using LeadTeams.Models.ViewModel.Meeting;
global using LeadTeams.Models.ViewModel.OrganizationNews;
global using LeadTeams.Models.ViewModel.Project;
global using LeadTeams.Models.ViewModel.ScreensAccessProfile;
global using LeadTeams.Models.ViewModel.ScreensAccessProfileDetails;
global using LeadTeams.Models.ViewModel.ScreenShotsMonitoring;
global using LeadTeams.Models.ViewModel.Setting;
global using LeadTeams.Models.ViewModel.Shift;
global using LeadTeams.Models.ViewModel.ShiftDynamicPattern;
global using LeadTeams.Models.ViewModel.ShiftFixedPattern;
global using LeadTeams.Models.ViewModel.Task;
global using LeadTeams.Models.ViewModel.User;
global using LeadTeams.PermissionAndSession;
global using LeadTeams.PermissionAndSession.Authentication;
global using LeadTeams.PermissionAndSession.Session;
global using LeadTeams.Services.API.DI;
global using LeadTeams.Services.API.Factory;
global using LeadTeams.Services.Core.Authentication;
global using LeadTeams.Services.Core.BaseService;
global using LeadTeams.Services.Core.Business;
global using LeadTeams.Services.Core.Reports;
global using LeadTeams.Shared.Helper;
global using LeadTeams.Shared.Windows.CustomControls.Message;
global using LeadTeams.Shared.Windows.Helper;
global using Microsoft.Extensions.DependencyInjection;
global using SignalRChat.Core.Infrastructure.ConnectionManager;
global using SignalRChat.Core.Modules.Login.Services;
global using SignalRChat.DI;
global using System.ComponentModel;
global using System.Data;
global using System.Diagnostics;
global using System.Reflection;
global using System.Runtime.CompilerServices;
