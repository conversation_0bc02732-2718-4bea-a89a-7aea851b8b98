﻿namespace LeadTeams.Models.ViewModel.ScreensAccessProfileDetails
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<IndexScreensAccessProfileDetailsViewModel>))]
    public class IndexScreensAccessProfileDetailsViewModel : BaseOrganizationWithoutTrackingModel
    {
        private Ulid screenId;
        private string screenText;
        private bool canShow;
        private bool canOpen;
        private bool canAdd;
        private bool canEdit;
        private bool canDelete;
        private bool canPrint;
        private bool selectAll;

        [DisplayName("Screen Id")]
        public Ulid ScreenId { get => screenId; set => CheckPropertyChanged(ref screenId, ref value); }
        [DisplayName("Screen Text")]
        public string ScreenText { get => screenText; set => CheckPropertyChanged(ref screenText, ref value); }
        [DisplayName("Can Show")]
        public bool CanShow { get => canShow; set => CheckPropertyChanged(ref canShow, ref value); }
        [DisplayName("Can Open")]
        public bool CanOpen { get => canOpen; set => CheckPropertyChanged(ref canOpen, ref value); }
        [DisplayName("Can Add")]
        public bool CanAdd { get => canAdd; set => CheckPropertyChanged(ref canAdd, ref value); }
        [DisplayName("Can Edit")]
        public bool CanEdit { get => canEdit; set => CheckPropertyChanged(ref canEdit, ref value); }
        [DisplayName("Can Delete")]
        public bool CanDelete { get => canDelete; set => CheckPropertyChanged(ref canDelete, ref value); }
        [DisplayName("Can Print")]
        public bool CanPrint { get => canPrint; set => CheckPropertyChanged(ref canPrint, ref value); }
        [DisplayName("Select All")]
        public bool SelectAll
        {
            get => selectAll; set
            {
                CheckPropertyChanged(ref selectAll, ref value);
                CanShow = value;
                CanOpen = value;
                CanAdd = value;
                CanEdit = value;
                CanDelete = value;
                CanPrint = value;
            }
        }
    }
}
