﻿namespace LeadTeams.API.Controllers.Business
{
    public class AskLeaveController : BaseBusinessController<AskLeaveModel, AskLeaveViewModel, CreateAskLeaveViewModel, UpdateAskLeaveViewModel>
    {
        private readonly IAskLeaveService _askLeaveService;

        public AskLeaveController(IAskLeaveService askLeaveService) : base(askLeaveService)
        {
            _askLeaveService = askLeaveService;
        }

        [HttpGet("SelectiveAskLeaveTypeList")]
        public IActionResult SelectiveAskLeaveTypeList()
        {
            try
            {
                var result = _askLeaveService.SelectiveAskLeaveTypeList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPatch("ApprovedAction")]
        public IActionResult ApprovedAction(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = _askLeaveService.ApprovedAction(ulid);

                if (result is false)
                    return BadRequest(new { Message = "Failed to approve entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPatch("RejectedAction")]
        public IActionResult RejectedAction(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = _askLeaveService.RejectedAction(ulid);

                if (result is false)
                    return BadRequest(new { Message = "Failed to reject entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
