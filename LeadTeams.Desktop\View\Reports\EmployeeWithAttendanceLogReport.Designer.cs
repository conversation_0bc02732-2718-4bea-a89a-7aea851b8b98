﻿namespace LeadTeams.Desktop.View.Reports
{
    partial class EmployeeWithAttendanceLogReport
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tableLayoutPanel1 = new LeadTeamsTableLayoutPanel();
            tpdPeriodValue = new TargetPatternDuration();
            tableLayoutPanel1.SuspendLayout();
            SuspendLayout();
            // 
            // RunningTotal
            // 
            RunningTotal.Name = "RunningTotal";
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.BackColor = Color.FromArgb(234, 242, 248);
            tableLayoutPanel1.ColumnCount = 2;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 405F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.Controls.Add(tpdPeriodValue, 0, 0);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.ForeColor = Color.FromArgb(22, 71, 117);
            tableLayoutPanel1.Location = new Point(0, 65);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RightToLeft = RightToLeft.No;
            tableLayoutPanel1.RowCount = 2;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 44F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.Size = new Size(784, 396);
            tableLayoutPanel1.TabIndex = 1;
            // 
            // tpdPeriodValue
            // 
            tpdPeriodValue.Dock = DockStyle.Fill;
            tpdPeriodValue.Location = new Point(3, 3);
            tpdPeriodValue.Name = "tpdPeriodValue";
            tpdPeriodValue.Size = new Size(399, 38);
            tpdPeriodValue.TabIndex = 3;
            // 
            // EmployeeWithAttendanceLogReport
            // 
            AutoScaleDimensions = new SizeF(9F, 21F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(784, 461);
            Controls.Add(tableLayoutPanel1);
            Margin = new Padding(4, 5, 4, 5);
            Name = "EmployeeWithAttendanceLogReport";
            Text = "EmployeeWithAttendanceLogReport";
            Controls.SetChildIndex(tableLayoutPanel1, 0);
            tableLayoutPanel1.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private LeadTeamsTableLayoutPanel tableLayoutPanel1;
        private TargetPatternDuration tpdPeriodValue;
    }
}