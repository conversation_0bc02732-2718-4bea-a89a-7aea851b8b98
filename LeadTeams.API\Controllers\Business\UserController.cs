﻿namespace LeadTeams.API.Controllers.Business
{
    public class UserController : BaseBusinessController<UserModel, UserViewModel, CreateUserViewModel, UpdateUserViewModel>
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService) : base(userService)
        {
            _userService = userService;
        }

        [HttpGet("SelectiveUserTypeList")]
        public IActionResult SelectiveUserTypeList()
        {
            try
            {
                var result = _userService.SelectiveUserTypeList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveEmployeeList")]
        public IActionResult SelectiveEmployeeList()
        {
            try
            {
                var result = _userService.SelectiveEmployeeList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveScreensAccessProfileList")]
        public IActionResult SelectiveScreensAccessProfileList()
        {
            try
            {
                var result = _userService.SelectiveScreensAccessProfileList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
