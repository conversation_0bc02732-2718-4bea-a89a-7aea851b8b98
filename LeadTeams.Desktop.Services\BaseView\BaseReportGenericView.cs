﻿namespace LeadTeams.Desktop.Services.BaseView
{
    public partial class BaseReportGenericView<TEntityView> : BaseReportView
        where TEntityView : new()
    {
        protected PaginationList<TEntityView> MainList;
        protected PaginationDataGridView dgvMain = new PaginationDataGridView()
        {
            Dock = DockStyle.Fill,
        };
        private TEntityView SearchValueModel = new TEntityView();

        public BaseReportGenericView()
        {
            InitializeComponent();

            AssociateAndRaiseEvents();

            //Pagination Events
            ApplyPaginationEvents(dgvMain);
        }

        private void AssociateAndRaiseEvents()
        {
            this.Shown += (s, e) => AddViewListControl();
        }

        public virtual PaginationList<TEntityView> MainData()
        {
            throw new NotImplementedException();
        }

        private void ApplyPaginationEvents(PaginationDataGridView dgvViewList)
        {
            dgvViewList.RefreshData += PaginationList_Refresh;
            dgvViewList.Previous += PaginationList_Refresh;
            dgvViewList.Next += PaginationList_Refresh;
            dgvViewList.First += PaginationList_Refresh;
            dgvViewList.Last += PaginationList_Refresh;
        }

        protected virtual void AddViewListControl()
        {
            throw new NotImplementedException();
        }

        private void LoadAllActionList()
        {
            LoadingHelper.ShowLoading("Loading ... Please wait ........", o =>
            {
                MainList = MainData();
                dgvMain.DataSource = MainList;
                if (dgvMain != null && MainList != null)
                    dgvMain.Header = $"Page {PageNumber} / {(MainList.TotalPages == 0 ? 1 : MainList.TotalPages)}";
            });
        }

        private void SearchAction()
        {
            if (DataSourceHandler == null)
            {
                MessageBox.Show("Need To Set The Data Source Handler So Can Start Serching");
                return;
            }

            SearchBoxGenericView<TEntityView> searchBox = new SearchBoxGenericView<TEntityView>(SearchValueModel, listControlValue, DataSourceHandler);
            if (searchBox.ShowDialog() == DialogResult.OK)
            {
                listControlValue = searchBox.listControlValue;
                SearchValueModel = searchBox.GetValuesAsModel<TEntityView>();
                LoadAllActionList();
            }
        }

        public override void Clear()
        {
            base.Clear();
            SearchValueModel = new TEntityView();
        }

        #region Buttons
        public override void RefreshListFunction()
        {
            LoadingHelper.ShowLoading("Loading ... Please wait ........", o =>
            {
                base.RefreshListFunction();
                LoadAllActionList();
            });
        }

        public override void ExportFunction()
        {
            base.ExportFunction();
            Excel.ExportFromDGV(dgvMain.GetDgvTotalsSummary.DGV);
        }

        public override void FilterFunction()
        {
            base.FilterFunction();
            SearchAction();
        }
        #endregion

        protected void SetColumnDisplayIndex(string columnName, int displayIndex)
        {
            if (dgvMain.GetDgvTotalsSummary.DGV.Columns.Contains(columnName))
                dgvMain.GetDgvTotalsSummary.DGV.Columns[columnName].DisplayIndex = displayIndex;
        }

        protected void SetColumnVisable(string columnName, bool visible)
        {
            if (dgvMain.GetDgvTotalsSummary.DGV.Columns.Contains(columnName))
                dgvMain.GetDgvTotalsSummary.DGV.Columns[columnName].Visible = visible;
        }

        #region Pagination
        protected int PageNumber => dgvMain.PageNumber;
        protected int PageSize => dgvMain.PageSize;

        private void PaginationList_Refresh(object? sender, EventArgs e)
        {
            LoadAllActionList();
        }
        #endregion
    }
}
