﻿namespace GMCadiomCore.WindowsStartup
{
    public class StartupManager
    {
        private const string RegistryPath = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run";
        private const string ShortcatPath = "Microsoft\\Windows\\Start Menu\\Programs";
        private readonly string ApplicationName;
        private readonly string ApplicationPath;
        private readonly bool InstallOnLocalMachine;

        public StartupManager(string applicationName, string applicationPath, bool installOnLocalMachine = false)
        {
            ApplicationName = applicationName;
            ApplicationPath = applicationPath;
            InstallOnLocalMachine = installOnLocalMachine;

            if (InstallOnLocalMachine && !IsAdmin() && IsWindowsVistaOrHigher())
                RestartElevated();
            else
                AddToStartup();
        }

        public static bool IsAdmin()
        {
            bool isAdmin;
            try
            {
                WindowsIdentity user = WindowsIdentity.GetCurrent();
                WindowsPrincipal principal = new WindowsPrincipal(user);
                isAdmin = principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch (UnauthorizedAccessException ex)
            {
                isAdmin = false;
            }
            catch (Exception ex)
            {
                isAdmin = false;
            }
            return isAdmin;
        }

        private static bool IsWindowsVistaOrHigher()
        {
            OperatingSystem os = Environment.OSVersion;
            return os.Platform == PlatformID.Win32NT && os.Version.Major >= 6;
        }

        private void AddToStartup()
        {
            //try
            //{
            //    string CommonStartup = Environment.GetFolderPath(Environment.SpecialFolder.CommonStartup);
            //    string ApplicationData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            //    string LocalApplicationData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            //    string CommonApplicationData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            //    ApplicationData = Path.Combine(ApplicationData, ShortcatPath);

            //    string ShortcutName = Path.GetFileNameWithoutExtension(ApplicationPath) + ".lnk";

            //    string fileCommonStartup = Path.Combine(CommonStartup, ShortcutName);
            //    string fileApplicationData = Path.Combine(ApplicationData, ShortcutName);

            //    CreateShortcut(fileCommonStartup, ApplicationPath);
            //    CreateShortcut(fileApplicationData, ApplicationPath);
            //}
            //catch { }

            if (InstallOnLocalMachine)
            {
                try
                {
                    using (RegistryKey main = Registry.LocalMachine)
                    {
                        using (RegistryKey? key = main.OpenSubKey(RegistryPath, true))
                        {
                            if (key != null)
                                if (key.GetValue(ApplicationName) == null)
                                    key.SetValue(ApplicationName, ApplicationPath);
                        }
                    }
                }
                catch { }
            }
            else
            {
                try
                {
                    using (RegistryKey main = Registry.CurrentUser)
                    {
                        using (RegistryKey? key = main.OpenSubKey(RegistryPath, true))
                        {
                            if (key != null)
                                if (key.GetValue(ApplicationName) == null)
                                    key.SetValue(ApplicationName, ApplicationPath);
                        }
                    }
                }
                catch { }
            }
        }

        private void CreateShortcut(string shortcutAddress, string TargetPath)
        {
            WshShell shell = new WshShell();
            IWshShortcut shortcut = (IWshShortcut)shell.CreateShortcut(shortcutAddress);
            shortcut.Description = "";
            shortcut.Hotkey = "";
            shortcut.TargetPath = TargetPath;
            shortcut.Save();
        }

        private void RestartElevated()
        {
            string[] argumentsArray = Environment.GetCommandLineArgs();
            string argumentsLine = string.Empty;

            for (int i = 1; i < argumentsArray.Length; ++i)
                argumentsLine += "\"" + argumentsArray[i] + "\" ";

            ProcessStartInfo info = new ProcessStartInfo();
            info.Arguments = argumentsLine.TrimEnd();
            info.FileName = ApplicationPath;
            info.UseShellExecute = true;
            info.Verb = "runas";
            info.WorkingDirectory = Environment.CurrentDirectory;

            try
            {
                Process.Start(info);
            }
            catch { return; }

            Environment.Exit(Environment.ExitCode);
        }
    }
}
