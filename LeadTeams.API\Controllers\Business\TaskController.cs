﻿namespace LeadTeams.API.Controllers.Business
{
    public class TaskController : BaseBusinessController<TaskModel, TaskViewModel, CreateTaskViewModel, UpdateTaskViewModel>
    {
        private readonly ITaskService _taskService;

        public TaskController(ITaskService taskService) : base(taskService)
        {
            _taskService = taskService;
        }

        [HttpGet("SelectiveEmployeeList")]
        public IActionResult SelectiveEmployeeList()
        {
            try
            {
                var result = _taskService.SelectiveEmployeeList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveProjectList")]
        public IActionResult SelectiveProjectList()
        {
            try
            {
                var result = _taskService.SelectiveProjectList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveTaskPrioritiesList")]
        public IActionResult SelectiveTaskPrioritiesList()
        {
            try
            {
                var result = _taskService.SelectiveTaskPrioritiesList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveTaskStatusList")]
        public IActionResult SelectiveTaskStatusList()
        {
            try
            {
                var result = _taskService.SelectiveTaskStatusList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetTask")]
        public IActionResult GetTask(Ulid? taskId)
        {
            try
            {
                var result = _taskService.GetTask(taskId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetTasksForEmployee")]
        public IActionResult GetTasksForEmployee(Ulid employeeId)
        {
            try
            {
                var result = _taskService.GetTasksForEmployee(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
