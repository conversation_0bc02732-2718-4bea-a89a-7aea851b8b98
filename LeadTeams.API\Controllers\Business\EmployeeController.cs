﻿namespace LeadTeams.API.Controllers.Business
{
    public class EmployeeController : BaseBusinessController<EmployeeModel, EmployeeViewModel, CreateEmployeeViewModel, UpdateEmployeeViewModel>
    {
        private readonly IEmployeeService _employeeService;

        public EmployeeController(IEmployeeService employeeService) : base(employeeService)
        {
            _employeeService = employeeService;
        }

        [HttpGet("SelectiveShiftList")]
        public IActionResult SelectiveShiftList()
        {
            try
            {
                var result = _employeeService.SelectiveShiftList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveMaritalStatusList")]
        public IActionResult SelectiveMaritalStatusList()
        {
            try
            {
                var result = _employeeService.SelectiveMaritalStatusList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveAllowanceList")]
        public IActionResult SelectiveAllowanceList()
        {
            try
            {
                var result = _employeeService.SelectiveAllowanceList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveTeamManagerList")]
        public IActionResult SelectiveTeamManagerList()
        {
            try
            {
                var result = _employeeService.SelectiveTeamManagerList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveTeamEmployeeList")]
        public IActionResult SelectiveTeamEmployeeList()
        {
            try
            {
                var result = _employeeService.SelectiveTeamEmployeeList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetEmployeeWorkingDayTime")]
        public IActionResult GetEmployeeWorkingDayTime(Ulid organizationId, Ulid employeeId, DateTime? day = null)
        {
            try
            {
                var result = _employeeService.GetEmployeeWorkingDayTime(organizationId, employeeId, day);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetEmployeeAllowancesForEmployee")]
        public IActionResult GetEmployeeAllowancesForEmployee(Ulid employeeId)
        {
            try
            {
                var result = _employeeService.GetEmployeeAllowancesForEmployee(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetEmployeeEducationalQualificationsForEmployee")]
        public IActionResult GetEmployeeEducationalQualificationsForEmployee(Ulid employeeId)
        {
            try
            {
                var result = _employeeService.GetEmployeeEducationalQualificationsForEmployee(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetEmployeeKidssForEmployee")]
        public IActionResult GetEmployeeKidssForEmployee(Ulid employeeId)
        {
            try
            {
                var result = _employeeService.GetEmployeeKidssForEmployee(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetEmployeesForManager")]
        public IActionResult GetEmployeesForManager(Ulid employeeId)
        {
            try
            {
                var result = _employeeService.GetEmployeesForManager(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetManagersForEmployee")]
        public IActionResult GetManagersForEmployee(Ulid employeeId)
        {
            try
            {
                var result = _employeeService.GetManagersForEmployee(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
