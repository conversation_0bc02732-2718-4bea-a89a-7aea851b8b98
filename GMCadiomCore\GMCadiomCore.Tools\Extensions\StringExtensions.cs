﻿namespace GMCadiomCore.Tools.Extensions
{
    public static class StringExtensions
    {
        public static int CountLines(this string str)
        {
            if (str == null)
                throw new ArgumentNullException("str");
            if (str == string.Empty)
                return 0;
            int index = -1;
            int count = 0;
            while (-1 != (index = str.IndexOf("\n", index + 1)))
                count++;

            return count + 1;
        }

        public static string SetOneLine(this string str)
        {
            if (str == null)
                throw new ArgumentNullException("str");
            if (str == string.Empty)
                return string.Empty;

            string value = str.Replace("\n", " ");

            return value;
        }
    }
}
