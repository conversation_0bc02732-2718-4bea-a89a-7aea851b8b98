﻿namespace LeadTeams.API.Controllers.Business
{
    public class MeetingController : BaseBusinessController<MeetingModel, MeetingViewModel, CreateMeetingViewModel, UpdateMeetingViewModel>
    {
        private readonly IMeetingService _meetingService;

        public MeetingController(IMeetingService meetingService) : base(meetingService)
        {
            _meetingService = meetingService;
        }

        [HttpGet("SelectiveEmployeeList")]
        public IActionResult SelectiveEmployeeList()
        {
            try
            {
                var result = _meetingService.SelectiveEmployeeList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("SelectiveProjectList")]
        public IActionResult SelectiveProjectList()
        {
            try
            {
                var result = _meetingService.SelectiveProjectList();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetEmployeesInMeeting")]
        public IActionResult GetEmployeesInMeeting(Ulid meetingId)
        {
            try
            {
                var result = _meetingService.GetEmployeesInMeeting(meetingId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
