﻿namespace GMCadiomCore.WindowsService
{
    public static class WindowsServiceCommands
    {
        public enum WindowsServiceHostType
        {
            Topshelf,
            WindowsService,
            WorkerService,
        }

        private const string ServiceControlCommand = "sc";
        private const string InstallCommand = "install";
        private const string CreateCommand = "create";
        private const string StopCommand = "stop";
        private const string StartCommand = "start";
        private const string DeleteCommand = "delete";
        private const string UninstallCommand = "uninstall";

        public static WindowsServiceHostType WindowsServiceHost { get; set; } = WindowsServiceHostType.Topshelf;

        public static async Task Create(string serviceName, string executablePath)
        {
            CommandResult res;
            switch (WindowsServiceHost)
            {
                case WindowsServiceHostType.WindowsService:
                case WindowsServiceHostType.WorkerService:
                    res = await Cli.Wrap(ServiceControlCommand)
                       .WithArguments(new[] { CreateCommand, serviceName, $"binPath={executablePath}", "start=auto" })
                       .ExecuteAsync();
                    break;
                case WindowsServiceHostType.Topshelf:
                    res = await Cli.Wrap(serviceName)
                        .WithArguments(new[] { InstallCommand })
                        .ExecuteAsync();
                    break;
            }
        }

        public static async Task Start(string serviceName)
        {
            CommandResult res;
            switch (WindowsServiceHost)
            {
                case WindowsServiceHostType.WindowsService:
                case WindowsServiceHostType.WorkerService:
                case WindowsServiceHostType.Topshelf:
                    res = await Cli.Wrap(ServiceControlCommand)
                        .WithArguments(new[] { StartCommand, serviceName })
                        .WithValidation(CommandResultValidation.None)
                        .ExecuteAsync();
                    break;
                    //case WindowsServiceHostType.Topshelf:
                    //    res = await Cli.Wrap(serviceName)
                    //        .WithArguments(new[] { StartCommand })
                    //        .WithValidation(CommandResultValidation.None)
                    //        .ExecuteAsync();
                    //    break;
            }
        }

        public static async Task Stop(string serviceName)
        {
            CommandResult res;
            switch (WindowsServiceHost)
            {
                case WindowsServiceHostType.WindowsService:
                case WindowsServiceHostType.WorkerService:
                case WindowsServiceHostType.Topshelf:
                    res = await Cli.Wrap(ServiceControlCommand)
                        .WithArguments(new[] { StopCommand, serviceName })
                        .WithValidation(CommandResultValidation.None)
                        .ExecuteAsync();
                    break;
                    //case WindowsServiceHostType.Topshelf:
                    //    res = await Cli.Wrap(serviceName)
                    //        .WithArguments(new[] { StopCommand })
                    //        .WithValidation(CommandResultValidation.None)
                    //        .ExecuteAsync();
                    //    break;
            }
        }

        public static async Task Delete(string serviceName)
        {
            CommandResult res;
            switch (WindowsServiceHost)
            {
                case WindowsServiceHostType.WindowsService:
                case WindowsServiceHostType.WorkerService:
                    res = await Cli.Wrap(ServiceControlCommand)
                        .WithArguments(new[] { DeleteCommand, serviceName })
                        .WithValidation(CommandResultValidation.None)
                        .ExecuteAsync();
                    break;
                case WindowsServiceHostType.Topshelf:
                    res = await Cli.Wrap(serviceName)
                        .WithArguments(new[] { UninstallCommand })
                        .WithValidation(CommandResultValidation.None)
                        .ExecuteAsync();
                    break;
            }
        }
    }
}
