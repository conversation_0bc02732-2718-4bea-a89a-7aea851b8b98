﻿namespace GMCadiomCore.Tools.Helper
{
    public class IPAddressHelper
    {
        public class IPAddress
        {
            public string IP { get; set; }
            public IPAddressType IPType { get; set; }
        }

        public enum IPAddressType
        {
            Public,
            Local
        }

        public static IPAddress GetIPAddress
        {
            get
            {
                IPAddress ipAddress = new IPAddress();
                try
                {
                    ipAddress.IP = GetPublicIPAddress();
                    ipAddress.IPType = IPAddressType.Public;
                }
                catch (WebException ex)
                {
                    switch (ex.Status)
                    {
                        case WebExceptionStatus.Timeout:
                            break;
                    }
                    ipAddress.IP = GetLocalIPAddress();
                    ipAddress.IPType = IPAddressType.Local;
                }
                catch (Exception ex)
                {
                    ipAddress.IP = GetLocalIPAddress();
                    ipAddress.IPType = IPAddressType.Local;
                }
                finally
                {
                }
                return ipAddress;
            }
        }

        private static string GetLocalIPAddress()
        {
            var host = Dns.GetHostEntry(Dns.GetHostName());
            foreach (var ip in host.AddressList)
            {
                if (ip.AddressFamily == AddressFamily.InterNetwork)
                {
                    return ip.ToString();
                }
            }
            return string.Empty;
        }

        private static string GetPublicIPAddress()
        {
            using (var client = new HttpClient())
            {
                var response = client.GetStringAsync("https://api.ipify.org").Result;
                return response;
            }
        }
    }
}
