﻿namespace LeadTeams.API.Controllers.Business
{
    public class AttendanceLogController : BaseBusinessController<AttendanceLogModel, AttendanceLogViewModel, CreateAttendanceLogViewModel, UpdateAttendanceLogViewModel>
    {
        private readonly IAttendanceLogService _attendanceLogService;

        public AttendanceLogController(IAttendanceLogService attendanceLogService) : base(attendanceLogService)
        {
            _attendanceLogService = attendanceLogService;
        }

        [HttpGet("GetLastAttendanceLog")]
        public IActionResult GetLastAttendanceLog(Ulid employeeId)
        {
            try
            {
                var result = _attendanceLogService.GetLastAttendanceLog(employeeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
