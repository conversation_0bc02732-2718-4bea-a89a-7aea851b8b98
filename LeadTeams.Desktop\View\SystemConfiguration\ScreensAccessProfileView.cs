﻿namespace LeadTeams.Desktop.View
{
    public partial class ScreensAccessProfileView : GenericViewTable.GenericScreensAccessProfileView
    {
        private readonly IScreensAccessProfileService _screensAccessProfileService;

        //Constructor
        public ScreensAccessProfileView(IScreensAccessProfileService screensAccessProfileService, ScreensAccessProfileModel? editModel = null) : base(screensAccessProfileService)
        {
            InitializeComponent();

            _screensAccessProfileService = screensAccessProfileService;

            AssociateAndRaiseEvents();
            LoadAllLists();
            ClearAndNew();

            if (editModel != null)
                SetUpdateEntity(editModel.ToUpdateDto());
        }

        private void AssociateAndRaiseEvents()
        {
            cbxSelectAllCanShow.CheckedChanged += (s, e) => SelectAllColumnAction(Actions.Show, cbxSelectAllCanShow.Checked);
            cbxSelectAllCanOpen.CheckedChanged += (s, e) => SelectAllColumnAction(Actions.Open, cbxSelectAllCanOpen.Checked);
            cbxSelectAllCanAdd.CheckedChanged += (s, e) => SelectAllColumnAction(Actions.Add, cbxSelectAllCanAdd.Checked);
            cbxSelectAllCanEdit.CheckedChanged += (s, e) => SelectAllColumnAction(Actions.Edit, cbxSelectAllCanEdit.Checked);
            cbxSelectAllCanDelete.CheckedChanged += (s, e) => SelectAllColumnAction(Actions.Delete, cbxSelectAllCanDelete.Checked);
            cbxSelectAllCanPrint.CheckedChanged += (s, e) => SelectAllColumnAction(Actions.Print, cbxSelectAllCanPrint.Checked);
            cbxSelectAll.CheckedChanged += (s, e) => SelectAllRowAction(cbxSelectAll.Checked);
            dgvScreensAccessProfileDetails.CurrentCellDirtyStateChanged += (s, e) => dgvScreensAccessProfileDetails.CommitEdit(DataGridViewDataErrorContexts.Commit);
            indexScreensAccessProfileDetailsViewModelBindingSource.DataSourceChanged += (s, e) => dgvScreensAccessProfileDetails.Columns[screenTextDataGridViewTextBoxColumn.Name].ReadOnly = true;
        }

        protected override void ClearAndNew()
        {
            base.ClearAndNew();

            txtScreensAccessProfileName.TextValue = string.Empty;
            Screens = PermissionAndSession.Screens.GetScreens.Select(x => x.ToViewDto()).ToList();
            cbxSelectAll.Checked = false;
        }

        protected override void LoadAllLists()
        {
            base.LoadAllLists();
        }

        //Keys
        public IList<IndexScreensAccessProfileDetailsViewModel> Screens
        {
            get => indexScreensAccessProfileDetailsViewModelBindingSource.List.OfType<IndexScreensAccessProfileDetailsViewModel>().ToList();
            set => indexScreensAccessProfileDetailsViewModelBindingSource.DataSource = value;
        }

        protected override CreateScreensAccessProfileViewModel CreateEntity()
        {
            CreateScreensAccessProfileViewModel model = new CreateScreensAccessProfileViewModel()
            {
                ScreensAccessProfileName = txtScreensAccessProfileName.TextValue,

                ScreensAccessProfileDetails = Screens.Select(x => x.ToCreateDto()).ToList(),

                OrganizationId = LocalSessionStatic.LoginSession.Employee.Organization.Id,
            };
            return model;
        }

        protected override void SetCreateEntity(CreateScreensAccessProfileViewModel model)
        {
            base.SetCreateEntity(model);

            txtScreensAccessProfileName.TextValue = model.ScreensAccessProfileName;

            var allScreens = PermissionAndSession.Screens.GetScreens.Select(x => x.ToViewDto()).ToList();
            foreach (var screen in allScreens)
            {
                var screenAccess = model.ScreensAccessProfileDetails.FirstOrDefault(x => x.ScreenId == screen.ScreenId);
                if (screenAccess != null)
                {
                    screen.CanShow = screenAccess.CanShow;
                    screen.CanOpen = screenAccess.CanOpen;
                    screen.CanAdd = screenAccess.CanAdd;
                    screen.CanEdit = screenAccess.CanEdit;
                    screen.CanDelete = screenAccess.CanDelete;
                    screen.CanPrint = screenAccess.CanPrint;
                }
            }
            Screens = allScreens;
        }

        protected override UpdateScreensAccessProfileViewModel UpdateEntity()
        {
            UpdateScreensAccessProfileViewModel model = new UpdateScreensAccessProfileViewModel()
            {
                Id = base.Id,
                ScreensAccessProfileName = txtScreensAccessProfileName.TextValue,

                ScreensAccessProfileDetails = Screens.Select(x => x.ToUpdateDto(base.Id)).ToList(),

                OrganizationId = LocalSessionStatic.LoginSession.Employee.Organization.Id,
            };
            return model;
        }

        protected override void SetUpdateEntity(UpdateScreensAccessProfileViewModel model)
        {
            base.SetUpdateEntity(model);

            txtScreensAccessProfileName.TextValue = model.ScreensAccessProfileName;
            var savedScreens = _screensAccessProfileService.GetScreensAccessProfileDetailsForScreensAccessProfile(model.Id);
            var allScreens = PermissionAndSession.Screens.GetScreens.Select(x => x.ToViewDto()).ToList();
            foreach (var screen in allScreens)
            {
                var screenAccess = savedScreens.FirstOrDefault(x => x.ScreenId == screen.ScreenId);
                if (screenAccess != null)
                {
                    screen.CanShow = screenAccess.CanShow;
                    screen.CanOpen = screenAccess.CanOpen;
                    screen.CanAdd = screenAccess.CanAdd;
                    screen.CanEdit = screenAccess.CanEdit;
                    screen.CanDelete = screenAccess.CanDelete;
                    screen.CanPrint = screenAccess.CanPrint;
                }
            }
            Screens = allScreens;
        }

        private void SelectAllColumnAction(Actions actions, bool value)
        {
            foreach (var screen in Screens)
            {
                switch (actions)
                {
                    case Actions.Show:
                        screen.CanShow = value;
                        break;
                    case Actions.Open:
                        screen.CanOpen = value;
                        break;
                    case Actions.Add:
                        screen.CanAdd = value;
                        break;
                    case Actions.Edit:
                        screen.CanEdit = value;
                        break;
                    case Actions.Delete:
                        screen.CanDelete = value;
                        break;
                    case Actions.Print:
                        screen.CanPrint = value;
                        break;
                }
            }
            indexScreensAccessProfileDetailsViewModelBindingSource.ResetBindings(false);
        }

        private void SelectAllRowAction(bool value)
        {
            foreach (var screen in Screens)
                screen.SelectAll = value;
            indexScreensAccessProfileDetailsViewModelBindingSource.ResetBindings(false);
        }
    }

    internal static class Mapping
    {
        internal static IndexScreensAccessProfileDetailsViewModel ToViewDto(this BaseScreensAccessTemplate template)
        {
            return new IndexScreensAccessProfileDetailsViewModel()
            {
                Id = template.Id,
                ScreenId = template.ScreenId,
                ScreenText = template.ScreenText,
                CanShow = template.CanShow,
                CanOpen = template.CanOpen,
                CanAdd = template.CanAdd,
                CanEdit = template.CanEdit,
                CanDelete = template.CanDelete,
                CanPrint = template.CanPrint,
            };
        }

        internal static CreateScreensAccessProfileDetailsViewModel ToCreateDto(this IndexScreensAccessProfileDetailsViewModel template)
        {
            return new CreateScreensAccessProfileDetailsViewModel()
            {
                ScreenId = template.ScreenId,
                CanShow = template.CanShow,
                CanOpen = template.CanOpen,
                CanAdd = template.CanAdd,
                CanEdit = template.CanEdit,
                CanDelete = template.CanDelete,
                CanPrint = template.CanPrint,
                OrganizationId = LocalSessionStatic.LoginSession.Employee.Organization.Id,
            };
        }

        internal static UpdateScreensAccessProfileDetailsViewModel ToUpdateDto(this IndexScreensAccessProfileDetailsViewModel template, Ulid screensAccessProfileId)
        {
            return new UpdateScreensAccessProfileDetailsViewModel()
            {
                Id = template.Id,
                ScreenId = template.ScreenId,
                CanShow = template.CanShow,
                CanOpen = template.CanOpen,
                CanAdd = template.CanAdd,
                CanEdit = template.CanEdit,
                CanDelete = template.CanDelete,
                CanPrint = template.CanPrint,
                ScreensAccessProfileId = screensAccessProfileId,
                OrganizationId = LocalSessionStatic.LoginSession.Employee.Organization.Id,
            };
        }
    }
}
